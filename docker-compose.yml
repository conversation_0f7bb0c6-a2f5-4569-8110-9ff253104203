version: '3.8'

services:
  # biFandBirdAgent - 智能营销系统主服务
  bifandbird-agent:
    image: registry.cn-hangzhou.aliyuncs.com/liuyab/bifandbird-agent:latest
    container_name: bifandbird-agent
    ports:
      - "28101:8000"
    environment:
      # 基础配置
      - HOST=${HOST:-0.0.0.0}
      - PORT=${PORT:-8000}
      - LOG_LEVEL=${LOG_LEVEL:-info}

      # CORS跨域配置 - 允许所有来源
      - CORS_ORIGINS=${CORS_ORIGINS:-*}

      # 数据库配置 (使用SQLite)
      - DATABASE_URL=${DATABASE_URL:-sqlite:///./bifandbird.db}
      
    volumes:
      # 持久化日志
      - ./logs:/app/logs
      # 如果需要挂载配置文件
      - ./.env:/app/.env:ro
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 重启策略
    restart: unless-stopped

    networks:
      - bifandbird-network

# 网络
networks:
  bifandbird-network:
    driver: bridge
