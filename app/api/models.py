"""
API数据模型定义

定义API请求和响应的数据结构，确保接口的类型安全和文档完整性。

Author: Marketing AI Team
Version: 2.0.0
Date: 2025-07-17
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime


class MarketingChatRequest(BaseModel):
    """
    前端与智能营销系统多轮对话请求模型
    userInput: 用户输入内容
    userId: 用户唯一标识（必填）
    conversationId: 对话ID（可选，首次可不传）
    sessionId: 会话ID（可选，用于短期记忆）
    """
    userInput: str = Field(
        ...,
        description="用户输入内容",
        example="我想做一个暑期促销活动，目标是大学生群体，预算2万元"
    )
    userId: str = Field(
        ...,
        description="用户ID，必传",
        example="user_12345"
    )
    conversationId: Optional[str] = Field(
        None,
        description="对话ID，首次可不传",
        example="conv_abc123"
    )
    sessionId: Optional[str] = Field(
        None,
        description="会话ID，用于短期记忆",
        example="sess_xyz789"
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "userInput": "我想做一个暑期促销活动",
                    "userId": "user_12345",
                    "conversationId": None,
                    "sessionId": None
                },
                {
                    "userInput": "目标是大学生群体，预算2万元，时间7月1日-31日",
                    "userId": "user_12345",
                    "conversationId": "conv_abc123",
                    "sessionId": "sess_xyz789"
                },
                {
                    "userInput": "我需要一个线上品牌推广方案，提升知名度，预算5万元，主要针对年轻用户",
                    "userId": "user_67890",
                    "conversationId": None,
                    "sessionId": None
                },
                {
                    "userInput": "我想做一个双11购物节活动，目标客群是25-35岁女性，预算10万元，主要在天猫和微信小程序推广",
                    "userId": "user_54321",
                    "conversationId": None,
                    "sessionId": None
                }
            ]
        }


class MarketingChatResponse(BaseModel):
    """
    智能营销系统多轮对话响应模型
    success: 是否成功
    data: 响应数据
    message: 响应消息
    conversationId: 对话ID，前端需保存
    sessionId: 会话ID
    """
    success: bool = Field(..., description="请求是否成功", example=True)
    data: Dict[str, Any] = Field(..., description="响应数据")
    message: str = Field(..., description="响应消息", example="处理完成")
    conversationId: str = Field(..., description="对话ID，前端需保存", example="conv_abc123")
    sessionId: str = Field(..., description="会话ID", example="sess_xyz789")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "success": True,
                    "data": {
                        "status": "interrupted",
                        "result": {
                            "response": "我需要更多信息来为您制定营销方案。请告诉我：1. 目标客户群体是什么？2. 预算范围是多少？3. 活动时间安排？",
                            "needCompletion": True,
                            "missingFields": {
                                "target_audience": "目标客户群体",
                                "budget": "预算范围",
                                "timeline": "活动时间"
                            }
                        },
                        "conversationId": "conv_abc123",
                        "threadId": "user_12345_conv_abc123"
                    },
                    "message": "需要补充信息",
                    "conversationId": "conv_abc123",
                    "sessionId": "sess_xyz789"
                },
                {
                    "success": True,
                    "data": {
                        "status": "completed",
                        "result": {
                            "response": "已为您生成完整的暑期促销方案！",
                            "planDraft": {
                                "title": "青春有你暑期促销活动",
                                "objective": "提升品牌在大学生群体中的知名度",
                                "target_audience": "18-25岁大学生",
                                "budget": "20000元",
                                "duration": "2024年7月1日-31日",
                                "channels": ["微博", "微信公众号", "抖音"],
                                "activities": [
                                    {
                                        "name": "转发微博抽奖",
                                        "description": "关注+转发指定微博，抽取小米手环",
                                        "prize": "小米手环 x 10个",
                                        "budget": "1500元"
                                    }
                                ]
                            }
                        },
                        "conversationId": "conv_abc123",
                        "threadId": "user_12345_conv_abc123"
                    },
                    "message": "方案生成完成",
                    "conversationId": "conv_abc123",
                    "sessionId": "sess_xyz789"
                },
                {
                    "success": False,
                    "data": {
                        "error": "用户输入为空",
                        "status": "error"
                    },
                    "message": "请求参数错误",
                    "conversationId": "",
                    "sessionId": ""
                }
            ]
        }


# 流式事件相关模型
class StreamEventModel(BaseModel):
    """增强版流式事件模型 - 支持更丰富的用户体验"""
    event: str = Field(..., description="事件类型")  # 修改为统一的event字段
    messageId: str = Field(..., description="消息ID，同一轮对话的所有事件共享相同messageId")
    conversationId: str = Field(..., description="对话ID")
    content: str = Field(default="", description="用户可读的文本内容")
    data: Optional[Dict[str, Any]] = Field(None, description="结构化数据（程序处理用）")
    createdAt: int = Field(default_factory=lambda: int(datetime.now().timestamp()), description="时间戳")

    # 保留一些向后兼容的字段（可选）
    nodeName: Optional[str] = Field(None, description="当前节点名称（向后兼容）")
    threadId: Optional[str] = Field(None, description="线程ID（向后兼容）")
    userId: Optional[str] = Field(None, description="用户ID（向后兼容）")

    # 用户体验增强字段
    progress: Optional[Dict[str, Any]] = Field(None, description="进度信息")
    userFriendlyData: Optional[Dict[str, Any]] = Field(None, description="用户友好的展示数据")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据信息")
    priority: Optional[str] = Field("normal", description="事件优先级：high/normal/low")
    category: Optional[str] = Field("system", description="事件分类：system/business/interaction/debug")
