from loguru import logger as loguru_logger

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse

from app.api.models import MarketingChatRequest, MarketingChatResponse
from app.services.marketingChatService import MarketingChatService
from app.services.streamingService import StreamingService

class MarketingRoutes:
    """
    营销路由控制器

    负责处理营销相关的HTTP请求，包括同步和流式接口。
    采用分层架构，将业务逻辑委托给服务层处理。
    """

    def __init__(self):
        """初始化营销路由控制器"""
        self.router = APIRouter(tags=["营销方案生成"])
        self.logger = loguru_logger.bind(controller="MarketingRoutes")

        # 初始化服务层组件
        self.marketingChatService = MarketingChatService()
        self.streamingService = StreamingService()

        # 设置路由
        self.setupRoutes()

    def setupRoutes(self):
        """设置路由配置"""

        # 1. 同步营销对话接口
        self.router.add_api_route(
            "/api/marketing/chat",
            self.marketingChatSync,
            methods=["POST"],
            response_model=MarketingChatResponse,
            summary="智能营销同步对话接口",
            description=self.getSyncApiDescription()
        )

        # 2. 流式营销对话接口
        @self.router.post(
            "/api/marketing/chat/stream",
            summary="智能营销流式对话接口",
            description=self.getStreamApiDescription()
        )
        async def marketingChatStreamEndpoint(request: MarketingChatRequest):
            """流式接口端点"""
            return await self.marketingChatStream(request)


    async def marketingChatSync(self, request: MarketingChatRequest) -> MarketingChatResponse:
        """
        智能营销同步对话接口

        Args:
            request: 营销对话请求

        Returns:
            MarketingChatResponse: 营销对话响应
        """
        try:
            self.logger.info(f"处理同步营销对话请求: userId={request.userId}")

            # 委托给服务层处理业务逻辑
            response = await self.marketingChatService.processChat(request)

            self.logger.info(f"同步营销对话处理完成: userId={request.userId}, success={response.success}")
            return response

        except Exception as e:
            self.logger.error(f"同步营销对话处理异常: {str(e)}")

            # 返回统一的错误响应
            return MarketingChatResponse(
                success=False,
                data={"error": f"服务器内部错误: {str(e)}"},
                message="处理请求时发生错误，请稍后重试",
                conversationId=request.conversationId or "",
                sessionId=request.sessionId or ""
            )

    async def marketingChatStream(self, request: MarketingChatRequest):
        """
        智能营销流式对话接口

        Args:
            request: 营销对话请求

        Returns:
            StreamingResponse: 流式响应
        """
        try:
            self.logger.info(f"处理流式营销对话请求: userId={request.userId}")

            # 1. 验证请求参数
            isValid, errorMessage = self.marketingChatService.validateRequest(request)
            if not isValid:
                raise HTTPException(status_code=400, detail=errorMessage)

            # 2. 准备工作流上下文
            context = self.marketingChatService.prepareWorkflowContext(request)

            # 3. 获取流式事件生成器
            eventGenerator = self.streamingService.processChatStream(request, context)

            # 4. 转换为SSE格式的流
            sseStream = self.streamingService.generateSseStream(eventGenerator)

            # 5. 返回流式响应
            return StreamingResponse(
                sseStream,
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
                    "Transfer-Encoding": "chunked",  # 启用分块传输
                    "Keep-Alive": "timeout=300, max=1000",  # 保持连接5分钟
                }
            )

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            self.logger.error(f"流式营销对话处理异常: {str(e)}")
            raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


    def getSyncApiDescription(self) -> str:
        """获取同步API的描述文档"""
        return """
前端与智能营销系统的主对话接口，支持多轮交互、短期/长期记忆。

**参数说明：**
- `userId`: 用户唯一标识（必填）
- `userInput`: 用户输入内容（如目标、预算、时间等）
- `conversationId`: 会话ID（可选，首次不传由后端生成，后续多轮对话需复用该ID）

**响应字段：**
- `status`: completed-流程已完成，interrupted-流程中断需补全
- `result`: 方案/补全信息/引导话术等
- `conversationId`: 会话ID（多轮对话需复用）
- `sessionId`: 会话标识（用于前端状态管理）

**请求示例：**
```json
{
  "userId": "user_12345",
  "userInput": "我想做一个暑期促销活动，目标是大学生群体，预算2万元，时间7月1日-31日",
  "conversationId": "conv_abc123"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "status": "completed",
    "result": {
      "response": "已为您生成完整的暑期促销方案...",
      "planDraft": {
        "title": "青春有你暑期促销活动",
        "target": "18-25岁大学生",
        "budget": "20000元",
        "duration": "2024年7月1日-31日"
      }
    },
    "conversationId": "conv_abc123"
  },
  "message": "处理完成",
  "conversationId": "conv_abc123",
  "sessionId": "sess_xyz789"
}
```

> 前端需保存 conversationId 和 sessionId，后续多轮对话均需携带 conversationId。
"""

    def getStreamApiDescription(self) -> str:
        """获取流式API的描述文档"""
        return """
流式响应接口，实时推送工作流执行进度和结果。

**事件类型说明：**
- `workflow_start`: 工作流开始
- `node_complete`: 节点执行完成
- `workflow_complete`: 工作流完成
- `workflow_error`: 执行错误

**使用方式：**
前端使用fetch + ReadableStream接收Server-Sent Events流式数据

**请求参数（JSON请求体）：**
- `userId`: 用户唯一标识（必填）
- `userInput`: 用户输入内容（必填）
- `conversationId`: 会话ID（可选）
- `sessionId`: 会话ID（可选）

**请求示例：**
```
POST /api/marketing/chat/stream
Content-Type: application/json

{
  "userId": "user_12345",
  "userInput": "我想做一个品牌推广活动",
  "conversationId": "conv_abc123"
}
```

**响应示例（SSE流式事件）：**
```
data: {"eventType":"workflow_start","message":"开始执行营销工作流","threadId":"user_12345_conv_abc123","timestamp":"2025-01-14T10:30:00.000Z"}

data: {"eventType":"node_complete","nodeName":"ROUTER_AGENT","message":"智能路由执行完成","threadId":"user_12345_conv_abc123","timestamp":"2025-01-14T10:30:02.000Z"}

data: {"eventType":"workflow_complete","message":"营销工作流执行完成","threadId":"user_12345_conv_abc123","timestamp":"2025-01-14T10:30:15.000Z"}
```

**前端JavaScript使用示例：**
```javascript
const response = await fetch('/api/marketing/chat/stream', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ userId: 'user123', userInput: '促销活动' })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    // 解析SSE数据...
}
```
"""



# 创建路由实例
marketing_routes = MarketingRoutes()
