"""
FastAPI应用主入口

配置和启动智能营销系统的API服务。

Author: Marketing AI Team
Version: 2.0.0
Date: 2025-06-22
"""

import logging
import re
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict

from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from loguru import logger

from app.core.exceptions import MarketingSystemError
from app.core.logging_config import setupLogging
from app.workflows.MarketingWorkflow import createWorkflow
from app.api.routes import marketing_routes
from app.core.config import config


def _is_allowed_origin(origin: str) -> bool:
    """
    检查是否为允许的来源

    支持的来源类型：
    - 本地开发环境 (localhost, 127.0.0.1)
    - 生产域名 (*.1game.fun, h.1game.fun)
    - 任意IP地址 (支持所有IPv4地址)
    - file:// 协议 (本地文件测试)
    """
    if not origin:
        return False

    # 允许 file:// 协议（本地文件测试）
    if origin.startswith("file://"):
        return True

    # 允许的来源模式
    allowed_patterns = [
        # 本地开发环境
        r"^https?://localhost(:\d+)?$",
        r"^https?://127\.0\.0\.1(:\d+)?$",

        # 生产域名
        r"^https?://.*\.1game\.fun(:\d+)?$",
        r"^https?://h\.1game\.fun(:\d+)?$",

        # 通用IP地址模式 - 允许所有IPv4地址
        r"^https?://(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(:\d+)?$",

        # 内网IP地址模式（更精确的匹配）
        r"^https?://10\.(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){2}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(:\d+)?$",
        r"^https?://172\.(?:1[6-9]|2[0-9]|3[0-1])\.(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){1}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(:\d+)?$",
        r"^https?://192\.168\.(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){1}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(:\d+)?$",
    ]

    for pattern in allowed_patterns:
        if re.match(pattern, origin):
            logger.debug(f"✅ CORS: 允许来源 {origin} (匹配模式: {pattern})")
            return True

    logger.warning(f"❌ CORS: 拒绝来源 {origin}")
    return False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理

    Args:
        app: FastAPI应用实例
    """
    try:
        # 步骤1: 系统初始化
        logger.info("🚀 智能营销系统启动中...")

        # 设置日志
        setupLogging()
        logger.info("📝 日志系统配置完成")

        # 步骤2: 验证LLM组件
        try:
            from app.llm.LlmFactory import getDefaultLLM
            import os

            llm = getDefaultLLM()

            # 获取LLM配置信息
            model_name = os.getenv("LLM_MODEL", "未知模型")
            api_base = os.getenv("OPENAI_API_BASE", "未知API地址")
            temperature = os.getenv("LLM_TEMPERATURE", "未知温度")
            max_tokens = os.getenv("LLM_MAX_TOKENS", "未知令牌数")

            # 提取API提供商信息
            provider = "未知提供商"
            if "dashscope.aliyuncs.com" in api_base:
                provider = "阿里云DashScope"
            elif "api.openai.com" in api_base:
                provider = "OpenAI"
            elif "api.deepseek.com" in api_base:
                provider = "DeepSeek"

            logger.info(f"🤖 大模型组件验证通过 - 模型: {model_name}, 提供商: {provider}, 温度: {temperature}, 最大令牌: {max_tokens}")

        except Exception as e:
            logger.error(f"❌ 大模型组件验证失败: {str(e)}")
            raise

        # 步骤3: 创建营销工作流
        logger.info("🔧 初始化营销工作流...")
        workflow = createWorkflow()
        logger.info("✅ 营销工作流初始化完成")

        logger.info("🎉 智能营销系统API服务启动完成")
        yield

    except Exception as e:
        logger.error(f"💥 应用启动失败: {str(e)}")
        raise
    finally:
        # 关闭时的清理
        logger.info("🔄 正在关闭智能营销系统API服务...")
        logger.info("👋 智能营销系统API服务已关闭")


def createApp() -> FastAPI:
    """
    创建FastAPI应用实例

    Returns:
        FastAPI: 配置好的应用实例
    """
    # 创建FastAPI应用
    app = FastAPI(
        title="智能营销方案生成系统",
        description="""
        ## 智能营销方案生成系统 API

        基于大语言模型和多Agent协作的智能营销活动方案生成系统。

        ### 主要功能
        - 🎯 **意图识别**: 从自然语言输入中提取营销意图
        - 👥 **客群分析**: 智能分析目标客户群体特征
        - 📊 **方案生成**: 生成完整的营销活动方案
        - 🔄 **流程监控**: 实时跟踪方案生成进度
        - 📈 **效果评估**: 预测和分析营销效果
        - 💬 **智能对话**: 类似Manus的多轮交互和实时反馈
        - 🔁 **反思循环**: 支持用户干预和方案迭代优化
        - 📚 **知识库RAG**: 基于历史营销活动的智能检索

        ### 使用流程
        1. 提交营销需求描述
        2. 系统自动分析和处理
        3. 查询处理状态和进度
        4. 获取完整的营销方案

        ### 技术特点
        - 🚀 异步处理，支持高并发
        - 🔒 企业级安全和稳定性
        - 📝 完整的API文档和示例
        - 🎛️ 灵活的配置和扩展性
        """,
        version="2.0.0",
        contact={
            "name": "Marketing AI Team",
            "email": "<EMAIL>"
        },
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT"
        },
        docs_url="/docs" ,
        redoc_url="/redoc" ,
        openapi_url="/openapi.json",
        lifespan=lifespan
    )

    # 配置CORS中间件 - 支持凭据的跨域请求，允许所有合法来源
    app.add_middleware(
        CORSMiddleware,
        # 使用正则表达式匹配所有需要的域名
        allow_origin_regex=r"https?://(localhost|127\.0\.0\.1|.*\.1game\.fun|1game\.fun)(:\d+)?",
        allow_credentials=True,  # 允许凭据（cookies, authorization headers等）
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
        max_age=3600,  # 预检请求缓存时间（1小时）
    )

    # 注册路由
    app.include_router(marketing_routes.router)

    # 健康检查端点
    @app.get("/api/v1/health")
    async def health_check():
        """健康检查端点"""
        return {"status": "healthy", "service": "biFandBirdAgent", "timestamp": datetime.now().isoformat()}

    return app


# 创建应用实例
app = createApp()


@app.exception_handler(MarketingSystemError)
async def marketingSystemErrorHandler(request: Request, exc: MarketingSystemError):
    """
    营销系统异常处理器

    Args:
        request: HTTP请求对象
        exc: 营销系统异常

    Returns:
        JSONResponse: 错误响应
    """
    errorContent = {
        "error": exc.error_code,
        "message": exc.message
    }

    return JSONResponse(
        status_code=400,
        content=jsonable_encoder(errorContent)
    )


@app.exception_handler(HTTPException)
async def httpExceptionHandler(request: Request, exc: HTTPException):
    """
    HTTP异常处理器

    Args:
        request: HTTP请求对象
        exc: HTTP异常

    Returns:
        JSONResponse: 错误响应
    """
    errorContent = {
        "error": "HTTPException",
        "message": str(exc.detail)
    }

    return JSONResponse(
        status_code=exc.status_code,
        content=jsonable_encoder(errorContent)
    )


@app.exception_handler(Exception)
async def globalExceptionHandler(request: Request, exc: Exception):
    """
    全局异常处理器

    Args:
        request: HTTP请求对象
        exc: 异常对象

    Returns:
        JSONResponse: 错误响应
    """
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)

    # 简化错误响应，避免复杂的数据结构
    errorContent = {
        "error": "InternalServerError",
        "message": "服务器内部错误，请稍后重试"
    }

    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(errorContent)
    )


@app.middleware("http")
async def loggingMiddleware(request: Request, call_next):
    """
    请求日志中间件

    Args:
        request: HTTP请求对象
        call_next: 下一个中间件或路由处理器

    Returns:
        Response: HTTP响应
    """
    import time

    # 记录请求开始
    start_time = time.time()
    client_ip = request.client.host if request.client else "unknown"

    logger.info(
        f"请求开始: {request.method} {request.url.path} "
        f"来源IP: {client_ip}"
    )

    # 处理请求
    response = await call_next(request)

    # 记录请求完成
    process_time = time.time() - start_time
    logger.info(
        f"请求完成: {request.method} {request.url.path} "
        f"状态码: {response.status_code} "
        f"耗时: {process_time:.3f}s"
    )

    return response


# 添加根路径和图标处理
@app.get("/")
async def root():
    """根路径重定向到API文档"""
    return {"message": "智能营销系统API服务", "docs": "/docs"}

@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    """网站图标处理"""
    return JSONResponse(content={}, status_code=204)
