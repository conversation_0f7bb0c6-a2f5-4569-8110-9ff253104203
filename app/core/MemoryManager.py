from datetime import datetime
from typing import Dict, Any
import uuid

class MemoryManager:
    def __init__(self):
        self.userMemories: Dict[str, dict] = {}  # 用户长期记忆，key: userId
        self.conversationStates: Dict[str, dict] = {}  # 对话状态，key: threadId
        self.sessionCache: Dict[str, dict] = {}  # 会话缓存，key: threadId

    def getUserMemory(self, userId: str) -> dict:
        """获取用户长期记忆"""
        if userId not in self.userMemories:
            self.userMemories[userId] = {
                "preferences": {},
                "history": [],
                "createdAt": datetime.now(),
                "lastActive": datetime.now()
            }
        return self.userMemories[userId]

    def updateUserMemory(self, userId: str, data: dict):
        """更新用户长期记忆"""
        memory = self.getUserMemory(userId)
        memory.update(data)
        memory["lastActive"] = datetime.now()

    def getConversationThreadId(self, userId: str, conversationId: str) -> str:
        """生成对话线程ID，唯一标识一个用户的一个对话"""
        return f"{userId}_{conversationId}"

    def createConversation(self, userId: str) -> str:
        """创建新对话，返回conversationId"""
        conversationId = str(uuid.uuid4())
        threadId = self.getConversationThreadId(userId, conversationId)
        self.conversationStates[threadId] = {
            "userId": userId,
            "conversationId": conversationId,
            "createdAt": datetime.now(),
            "lastMessage": datetime.now(),
            "messageCount": 0,
            "status": "active"
        }
        return conversationId

    def getConversationState(self, threadId: str) -> dict:
        """获取对话状态"""
        return self.conversationStates.get(threadId, {})

    def updateConversationState(self, threadId: str, data: dict):
        """更新对话状态"""
        state = self.conversationStates.get(threadId, {})
        state.update(data)
        state["lastMessage"] = datetime.now()
        self.conversationStates[threadId] = state

    def getSessionCache(self, threadId: str) -> dict:
        """获取会话缓存"""
        return self.sessionCache.get(threadId, {})

    def updateSessionCache(self, threadId: str, data: dict):
        """更新会话缓存"""
        cache = self.sessionCache.get(threadId, {})
        cache.update(data)
        self.sessionCache[threadId] = cache 