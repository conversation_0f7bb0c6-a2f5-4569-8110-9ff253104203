"""
统一的Checkpointer接口，支持多种存储后端
"""

import os
from langgraph.checkpoint.memory import MemorySaver

# 尝试导入Redis相关模块
try:
    import redis
    from langgraph.checkpoint.redis import RedisSaver
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None
    RedisSaver = None

# Redis配置
REDIS_CONFIG = {
    "host": os.getenv("REDIS_HOST", "h.1game.fun"),
    "port": int(os.getenv("REDIS_PORT", "6379")),
    "db": int(os.getenv("REDIS_DB", "0")),
    "password": os.getenv("REDIS_PASSWORD", "liuyb"),
    "decode_responses": True,
    "socket_connect_timeout": int(os.getenv("REDIS_TIMEOUT", "5")),
    "socket_timeout": int(os.getenv("REDIS_TIMEOUT", "5"))
}


def getDefaultSaver(mode: str = None):
    """
    获取默认的Saver实例
    mode: "memory" 或 "redis"，如果为None则从环境变量读取
    """
    # 如果没有指定mode，从环境变量读取
    if mode is None:
        mode = os.getenv("CHECKPOINTER_MODE", "memory")

    if mode == "redis" and REDIS_AVAILABLE:
        try:
            redis_client = redis.Redis(**REDIS_CONFIG)
            redis_client.ping()
            return RedisSaver(redis_client=redis_client)
        except Exception:
            return MemorySaver()
    else:
        return MemorySaver()
