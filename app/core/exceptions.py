"""
智能营销系统 - 异常处理模块

定义了系统中使用的所有自定义异常类，提供统一的错误处理机制。

Author: Marketing AI Team
Version: 2.0.0
Date: 2025-06-22
"""

from typing import Optional, Dict, Any


class MarketingSystemError(Exception):
    """营销系统基础异常类"""
    
    def __init__(self, message: str, errorCode: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            errorCode: 错误代码
            details: 错误详情
        """
        super().__init__(message)
        self.message = message
        self.errorCode = errorCode or "UNKNOWN_ERROR"
        self.details = details or {}
    
    def toDict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "errorType": self.__class__.__name__,
            "errorCode": self.errorCode,
            "message": self.message,
            "details": self.details
        }


class ConfigurationError(MarketingSystemError):
    """配置错误"""
    
    def __init__(self, message: str, configKey: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="CONFIG_ERROR",
            details={"configKey": configKey} if configKey else {}
        )


class AgentError(MarketingSystemError):
    """Agent执行错误"""
    
    def __init__(self, message: str, agentName: Optional[str] = None, 
                 agentStep: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="AGENT_ERROR",
            details={
                "agentName": agentName,
                "agentStep": agentStep
            }
        )


class WorkflowError(MarketingSystemError):
    """工作流执行错误"""
    
    def __init__(self, message: str, workflowStep: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="WORKFLOW_ERROR",
            details={"workflowStep": workflowStep}
        )


class LLMError(MarketingSystemError):
    """大语言模型调用错误"""
    
    def __init__(self, message: str, modelName: Optional[str] = None, 
                 prompt: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="LLM_ERROR",
            details={
                "modelName": modelName,
                "prompt": prompt[:200] + "..." if prompt and len(prompt) > 200 else prompt
            }
        )


class ValidationError(MarketingSystemError):
    """数据验证错误"""
    
    def __init__(self, message: str, fieldName: Optional[str] = None, 
                 fieldValue: Optional[Any] = None):
        super().__init__(
            message=message,
            errorCode="VALIDATION_ERROR",
            details={
                "fieldName": fieldName,
                "fieldValue": str(fieldValue) if fieldValue is not None else None
            }
        )


class ExternalAPIError(MarketingSystemError):
    """外部API调用错误"""
    
    def __init__(self, message: str, apiName: Optional[str] = None, 
                 statusCode: Optional[int] = None, response: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="EXTERNAL_API_ERROR",
            details={
                "apiName": apiName,
                "statusCode": statusCode,
                "response": response[:500] + "..." if response and len(response) > 500 else response
            }
        )


class ResourceNotFoundError(MarketingSystemError):
    """资源未找到错误"""
    
    def __init__(self, message: str, resourceType: Optional[str] = None, 
                 resourceId: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="RESOURCE_NOT_FOUND",
            details={
                "resourceType": resourceType,
                "resourceId": resourceId
            }
        )


class AuthenticationError(MarketingSystemError):
    """认证错误"""
    
    def __init__(self, message: str, authType: Optional[str] = None):
        super().__init__(
            message=message,
            errorCode="AUTHENTICATION_ERROR",
            details={"authType": authType}
        )


class RateLimitError(MarketingSystemError):
    """频率限制错误"""
    
    def __init__(self, message: str, limitType: Optional[str] = None, 
                 retryAfter: Optional[int] = None):
        super().__init__(
            message=message,
            errorCode="RATE_LIMIT_ERROR",
            details={
                "limitType": limitType,
                "retryAfter": retryAfter
            }
        )
