"""
极简配置，仅保留核心参数
"""
import os

class Config:
    def __init__(self):
        # 服务配置
        self.host = "0.0.0.0"  # 服务监听地址
        self.port = 8000       # 服务端口

        # 应用信息
        self.APP_NAME = "智能营销系统"  # 应用名称，用于日志文件名等

        # CORS配置 - 简化配置，统一在app.py中处理
        self.CORS_ORIGINS = ["*"]  # 允许所有来源，具体验证在中间件中处理
        
        # 日志配置
        self.LOG_DIR = "./logs"                # 日志存储目录
        self.log_level = "debug"               # 日志级别: debug, info, warning, error, critical
        self.log_rotation = "100 MB"           # 单个日志文件大小限制
        self.log_retention = "30 days"         # 日志保留时间
        self.log_compression = "zip"           # 日志压缩格式
        
        # 第三方服务日志级别（可选，使用默认值即可）
        self.third_party_log_levels = {
            "uvicorn": "INFO",
            "uvicorn.error": "INFO",
            "uvicorn.access": "INFO",
            "fastapi": "INFO",
            "httpx": "WARNING",
            "langchain": "WARNING",
            "openai": "WARNING"
        }



# 实例化 config，供外部直接 import 使用
config = Config()

# 兼容老代码的模块变量
HOST = config.host
PORT = config.port
LOG_DIR = config.LOG_DIR
APP_NAME = config.APP_NAME
LOG_LEVEL = config.log_level

# 如需扩展，直接加变量即可
