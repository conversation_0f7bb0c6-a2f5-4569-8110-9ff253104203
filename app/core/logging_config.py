"""
日志配置模块

配置系统的日志记录，支持文件输出、控制台输出和结构化日志。

Author: Marketing AI Team
Version: 2.0.0
Date: 2025-06-22
"""

import os
import sys
import logging
import logging.handlers
from typing import Dict, Any
from pathlib import Path

from loguru import logger as loguru_logger
from app.core.config import config


class InterceptHandler(logging.Handler):
    """
    拦截标准库日志并转发到loguru
    """
    
    def emit(self, record):
        """发送日志记录"""
        # 获取对应的loguru级别
        try:
            level = loguru_logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        # 查找调用者
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        loguru_logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setupLogging():
    """
    设置系统日志配置
    """
    # 移除默认的loguru处理器
    loguru_logger.remove()
    
    # 创建日志目录
    logDir = Path(config.LOG_DIR)
    logDir.mkdir(parents=True, exist_ok=True)
    
    # 配置控制台输出
    loguru_logger.add(
        sys.stderr,
        level=config.log_level.upper(),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 配置文件输出
    logFilePath = logDir / f"{config.APP_NAME.lower().replace(' ', '_')}.log"
    loguru_logger.add(
        str(logFilePath),
        level=config.log_level.upper(),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="100 MB",  # 默认100MB
        retention="30 days",  # 默认保留30天
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 配置错误日志文件
    errorLogPath = logDir / f"{config.APP_NAME.lower().replace(' ', '_')}_error.log"
    loguru_logger.add(
        str(errorLogPath),
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="100 MB",  # 默认100MB
        retention="30 days",  # 默认保留30天
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 拦截标准库日志
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # 设置第三方库的日志级别
    loggingConfig = {
        "uvicorn": "INFO",
        "uvicorn.error": "INFO",
        "uvicorn.access": "INFO",
        "fastapi": "INFO",
        "httpx": "ERROR",  # 减少HTTP请求日志
        "langchain": "ERROR",  # 减少LangChain内部日志
        "openai": "ERROR",  # 减少OpenAI API日志
        "langsmith": "ERROR",  # 减少LangSmith追踪日志
        "httpcore": "ERROR",  # 减少HTTP核心日志
        "urllib3": "ERROR"  # 减少urllib3日志
    }
    
    for loggerName, level in loggingConfig.items():
        logging.getLogger(loggerName).setLevel(getattr(logging, level))
    
    # 记录启动信息
    loguru_logger.info(f"📝 日志系统初始化完成 - 应用: {config.APP_NAME}, 级别: {config.log_level.upper()}, 目录: {logDir}")


def getLogger(name: str = None):
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        Logger: 日志记录器实例
    """
    if name:
        return loguru_logger.bind(name=name)
    return loguru_logger


def logPerformance(func):
    """
    性能日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            loguru_logger.info(
                f"函数 {func.__name__} 执行完成，耗时: {execution_time:.3f}秒"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            loguru_logger.error(
                f"函数 {func.__name__} 执行失败，耗时: {execution_time:.3f}秒，错误: {str(e)}"
            )
            
            raise
    
    return wrapper


def logAsyncPerformance(func):
    """
    异步性能日志装饰器
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        装饰后的异步函数
    """
    import time
    import functools
    
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            loguru_logger.info(
                f"异步函数 {func.__name__} 执行完成，耗时: {execution_time:.3f}秒"
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            loguru_logger.error(
                f"异步函数 {func.__name__} 执行失败，耗时: {execution_time:.3f}秒，错误: {str(e)}"
            )
            
            raise
    
    return wrapper


class StructuredLogger:
    """
    结构化日志记录器
    """
    
    def __init__(self, name: str):
        """
        初始化结构化日志记录器
        
        Args:
            name: 日志记录器名称
        """
        self.logger = loguru_logger.bind(component=name)
        self.name = name
    
    def logEvent(self, event: str, level: str = "INFO", **kwargs):
        """
        记录结构化事件
        
        Args:
            event: 事件名称
            level: 日志级别
            **kwargs: 事件数据
        """
        logData = {
            "event": event,
            "component": self.name,
            **kwargs
        }
        
        self.logger.log(level, f"事件: {event}", extra=logData)
    
    def logMetric(self, metric: str, value: float, unit: str = "", **kwargs):
        """
        记录指标数据
        
        Args:
            metric: 指标名称
            value: 指标值
            unit: 单位
            **kwargs: 额外数据
        """
        logData = {
            "metric": metric,
            "value": value,
            "unit": unit,
            "component": self.name,
            **kwargs
        }
        
        self.logger.info(f"指标: {metric} = {value} {unit}", extra=logData)
    
    def logError(self, error: Exception, context: Dict[str, Any] = None):
        """
        记录错误信息
        
        Args:
            error: 异常对象
            context: 错误上下文
        """
        logData = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "component": self.name,
            **(context or {})
        }
        
        self.logger.error(f"错误: {str(error)}", extra=logData)


# 预定义的结构化日志记录器
workflowLogger = StructuredLogger("workflow")
agentLogger = StructuredLogger("agent")
apiLogger = StructuredLogger("api")
llmLogger = StructuredLogger("llm")
