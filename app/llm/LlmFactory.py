import os
from dotenv import load_dotenv, find_dotenv

_ = load_dotenv(find_dotenv())

from langchain_openai import ChatOpenAI


# 初始化LLM
qwq_32b_llm = ChatOpenAI(
    model= os.getenv("LLM_MODEL"),
    temperature=os.getenv("LLM_TEMPERATURE"),
    api_key=os.getenv("OPENAI_API_KEY"),
    max_tokens=os.getenv("LLM_MAX_TOKENS"),
    base_url=os.getenv("OPENAI_API_BASE"),
    streaming=True  # 启用流式模式，DashScope 模型要求
)
# 获取默认的大模型
def getDefaultLLM():
    """
    获取默认的大模型

    Returns:
        ChatOpenAI: 默认的大模型
    """
    return qwq_32b_llm


# 获取默认的大模型
def getDefaultEmbeddingLLM():
    """
    获取默认的大模型

    Returns:
        ChatOpenAI: 默认的大模型
    """
    return qwq_32b_llm
