"""
对话管理服务

负责管理对话状态、用户记忆和会话缓存，包括：
1. 对话生命周期管理
2. 用户记忆更新和维护
3. 对话状态持久化
4. 会话上下文管理
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger

from app.api.models import MarketingChatRequest
from app.core.MemoryManager import MemoryManager


class ConversationContext:
    """对话上下文数据结构"""
    
    def __init__(self,
                 conversationState: Dict[str, Any],
                 sessionCache: Dict[str, Any],
                 userMemory: Dict[str, Any]):
        self.conversationState = conversationState
        self.sessionCache = sessionCache
        self.userMemory = userMemory


class ConversationService:
    """
    对话管理服务
    
    提供对话状态管理、用户记忆维护等功能。
    负责处理多轮对话的上下文保持和状态持久化。
    """
    
    def __init__(self, memoryManager: Optional[MemoryManager] = None):
        """
        初始化对话管理服务
        
        Args:
            memoryManager: 内存管理器实例，如果为None则创建新实例
        """
        self.logger = logger.bind(service="ConversationService")
        self.memoryManager = memoryManager or MemoryManager()
        
    def createConversation(self, userId: str) -> str:
        """
        创建新对话
        
        Args:
            userId: 用户ID
            
        Returns:
            str: 新创建的对话ID
        """
        conversationId = str(uuid.uuid4())
        threadId = self.generateThreadId(userId, conversationId)
        
        # 初始化对话状态
        initialState = {
            "userId": userId,
            "conversationId": conversationId,
            "createdAt": datetime.now().isoformat(),
            "lastMessage": datetime.now().isoformat(),
            "messageCount": 0,
            "status": "active",
            "isStreamRequest": False
        }
        
        self.memoryManager.updateConversationState(threadId, initialState)
        
        self.logger.info(f"创建新对话: userId={userId}, conversationId={conversationId}")
        return conversationId
        
    def generateThreadId(self, userId: str, conversationId: str) -> str:
        """
        生成线程ID
        
        Args:
            userId: 用户ID
            conversationId: 对话ID
            
        Returns:
            str: 线程ID
        """
        return f"{userId}_{conversationId}"
        
    def getConversationContext(self, threadId: str) -> ConversationContext:
        """
        获取对话上下文
        
        Args:
            threadId: 线程ID
            
        Returns:
            ConversationContext: 对话上下文
        """
        conversationState = self.memoryManager.getConversationState(threadId)
        sessionCache = self.memoryManager.getSessionCache(threadId)
        
        # 从对话状态中提取用户ID
        userId = conversationState.get("userId", "")
        userMemory = self.memoryManager.getUserMemory(userId) if userId else {}
        
        return ConversationContext(
            conversationState=conversationState,
            sessionCache=sessionCache,
            userMemory=userMemory
        )
        
    def updateConversationState(self, threadId: str, updates: Dict[str, Any], isStreamRequest: bool = False):
        """
        更新对话状态
        
        Args:
            threadId: 线程ID
            updates: 更新数据
            isStreamRequest: 是否为流式请求
        """
        currentState = self.memoryManager.getConversationState(threadId)
        messageCount = currentState.get("messageCount", 0) + 1
        
        stateUpdates = {
            "messageCount": messageCount,
            "lastMessage": datetime.now().isoformat(),
            "isStreamRequest": isStreamRequest,
            **updates
        }
        
        self.memoryManager.updateConversationState(threadId, stateUpdates)
        self.logger.debug(f"更新对话状态: threadId={threadId}, messageCount={messageCount}")
        
    def updateUserMemory(self, userId: str, request: MarketingChatRequest, 
                        workflowResult: Dict[str, Any], conversationId: str):
        """
        更新用户记忆
        
        Args:
            userId: 用户ID
            request: 营销对话请求
            workflowResult: 工作流执行结果
            conversationId: 对话ID
        """
        userMemory = self.memoryManager.getUserMemory(userId)
        
        # 添加新的交互记录
        interactionRecord = {
            "conversationId": conversationId,
            "userInput": request.userInput,
            "result": workflowResult,
            "timestamp": datetime.now().isoformat()
        }
        
        userMemory["history"].append(interactionRecord)
        
        # 保持历史记录在合理范围内（最多50条）
        if len(userMemory["history"]) > 50:
            userMemory["history"] = userMemory["history"][-50:]
            
        # 更新用户活跃时间
        userMemory["lastActive"] = datetime.now().isoformat()
        
        self.memoryManager.updateUserMemory(userId, userMemory)
        self.logger.debug(f"更新用户记忆: userId={userId}, 历史记录数={len(userMemory['history'])}")
        
    def updateAfterWorkflow(self, request: MarketingChatRequest, 
                           workflowResult: Dict[str, Any], 
                           context: Any,
                           isStreamRequest: bool = False):
        """
        工作流执行后的统一更新操作
        
        Args:
            request: 营销对话请求
            workflowResult: 工作流执行结果
            context: 工作流上下文（包含threadId, conversationId等）
            isStreamRequest: 是否为流式请求
        """
        # 1. 更新用户记忆
        self.updateUserMemory(
            request.userId, 
            request, 
            workflowResult, 
            context.conversationId
        )
        
        # 2. 更新对话状态
        stateUpdates = {
            "lastResult": workflowResult,  # 保存完整结果用于调试
            "status": workflowResult.get("status", "completed")
        }
        
        self.updateConversationState(
            context.threadId, 
            stateUpdates, 
            isStreamRequest
        )
        
        self.logger.info(f"工作流后更新完成: threadId={context.threadId}")
        

