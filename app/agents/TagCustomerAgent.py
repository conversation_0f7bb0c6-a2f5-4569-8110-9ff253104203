"""
客群分析Agent，负责分析目标客群特征
"""

import json
from typing import Dict, Any
from app.agents.base import LLMAgent, AgentResult
from app.utils.models import CustomerTags, MarketingState
from app.llm.LlmFactory import getDefaultLLM
from app.utils.prompts import customerAnalysisPrompt
from app.agents.base import agentLogDecorator
from app.utils.llm_utils import processLLMJsonResponse


class TagCustomerAgent(LLMAgent):
    """
    客群分析Agent
    
    负责分析目标客群特征，输出客群标签结构
    """
    
    def __init__(self):
        """初始化客群分析Agent"""
        super().__init__("TagCustomerAgent", getDefaultLLM())
    
    @agentLogDecorator
    def execute(self, state: MarketingState) -> MarketingState:
        """
        客群标签Agent核心逻辑
        
        Args:
            state: 当前状态
            
        Returns:
            MarketingState: 更新后的状态
        """
        try:
            # 获取意图信息
            intentInfo = state.get("intentInfo")
            
            if not intentInfo:
                return state
            
            # 使用LLM分析客群
            customerTags = self._analyzeCustomerWithLLM(intentInfo)
            
            if not customerTags:
                return state
            
            # 更新状态
            state.update({
                "customerTags": customerTags,
                "currentStep": "customer_analysis_completed"
            })
            
            return state
            
        except Exception as e:
            self.logger.error(f"客群标签Agent执行失败: {str(e)}")
            return state
    
    def _analyzeCustomerWithLLM(self, intentInfo) -> CustomerTags:
        """
        使用LLM分析客群特征

        Args:
            intentInfo: 意图信息

        Returns:
            CustomerTags: 客群标签信息
        """
        try:
            # 构造prompt
            prompt = customerAnalysisPrompt.format(intentInfo=json.dumps(intentInfo))

            # 定义默认值
            default_tags = {
                "tags": ["年轻用户", "高活跃度", "数字渠道偏好"],
                "characteristics": ["18-25岁", "大学生", "经常使用APP"],
                "preferences": {"渠道": "APP/微信", "产品": "理财/抽奖"},
                "behaviors": ["频繁登录", "参与活动"],
                "segments": ["校园用户", "潜力客户"],
                "completed": False,
                "error": "LLM分析失败，使用默认结构"
            }

            # 调用LLM并处理响应
            response = self.llm.invoke(prompt)
            tagsData = processLLMJsonResponse(response, default_value=default_tags)
            
            if tagsData and isinstance(tagsData, dict) and tagsData.get("tags"):
                customerTags = CustomerTags(
                    tags=tagsData.get("tags", []),
                    structure=tagsData.get("structure", {})
                )
                return customerTags
            else:
                # 没有分析出结果，返回写死的默认结构
                return {
                    "tags": ["年轻用户", "高活跃度", "数字渠道偏好"],
                    "characteristics": ["18-25岁", "大学生", "经常使用APP"],
                    "preferences": {"渠道": "APP/微信", "产品": "理财/抽奖"},
                    "behaviors": ["频繁登录", "参与活动"],
                    "segments": ["校园用户", "潜力客户"],
                    "completed": False,
                    "error": "未能分析出客群，已返回默认结构"
                }
        except Exception as e:
            self.logger.error(f"LLM客群分析失败: {str(e)}")
            # 返回写死的默认客群结构
            return {
                "tags": ["年轻用户", "高活跃度", "数字渠道偏好"],
                "characteristics": ["18-25岁", "大学生", "经常使用APP"],
                "preferences": {"渠道": "APP/微信", "产品": "理财/抽奖"},
                "behaviors": ["频繁登录", "参与活动"],
                "segments": ["校园用户", "潜力客户"],
                "completed": False,
                "error": str(e)
            } 