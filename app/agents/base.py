"""
基础Agent类，提供统一的Agent接口和基本功能
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import functools
from loguru import logger as loguru_logger

from app.utils.models import MarketingState


class AgentStatus(Enum):
    """Agent执行状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    SKIPPED = "skipped"


@dataclass
class AgentResult:
    """
    Agent执行结果封装类

    统一封装Agent的执行结果，包含状态、数据、错误信息等。
    """
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status: AgentStatus = AgentStatus.SUCCESS
    metadata: Optional[Dict[str, Any]] = None

    @classmethod
    def success_result(cls, data: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> 'AgentResult':
        """创建成功结果"""
        return cls(
            success=True,
            data=data,
            status=AgentStatus.SUCCESS,
            metadata=metadata or {}
        )

    @classmethod
    def failed_result(cls, error: str, data: Optional[Dict[str, Any]] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> 'AgentResult':
        """创建失败结果"""
        return cls(
            success=False,
            data=data,
            error=error,
            status=AgentStatus.FAILED,
            metadata=metadata
        )

    @classmethod
    def partial_result(cls, data: Dict[str, Any], error: str,
                      metadata: Optional[Dict[str, Any]] = None) -> 'AgentResult':
        """创建部分成功结果"""
        return cls(
            success=True,
            data=data,
            error=error,
            status=AgentStatus.PARTIAL,
            metadata=metadata or {}
        )


def agentLogDecorator(func):
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        logger = loguru_logger.bind(agent=self.__class__.__name__)
        logger.info(f"开始执行: {self.__class__.__name__}")
        result = func(self, *args, **kwargs)
        logger.info(f"完成执行: {self.__class__.__name__}，结果: {getattr(result, 'success', None)}")
        return result
    return wrapper


class BaseAgent(ABC):
    """
    Agent基类

    定义了所有Agent的通用接口和基础功能。
    所有具体的Agent都应该继承此类并实现抽象方法。
    """

    def __init__(self, name: str):
        """
        初始化Agent

        Args:
            name: Agent名称
        """
        self.name = name
        try:
            self.logger = loguru_logger.bind(agent=name)
        except:
            self.logger = loguru_logger.bind(agent=name)

    @agentLogDecorator
    @abstractmethod
    def execute(self, state: Dict[str, Any]) -> AgentResult:
        """
        执行Agent核心逻辑

        Args:
            state: 当前状态

        Returns:
            AgentResult: 执行结果
        """
        pass

    def validateInput(self, state: Dict[str, Any]) -> bool:
        """
        验证输入状态

        Args:
            state: 当前状态

        Returns:
            bool: 验证结果
        """
        return True

    def get_required_fields(self) -> list[str]:
        """
        获取Agent执行所需的状态字段

        Returns:
            list[str]: 必需字段列表
        """
        return []

    def get_output_fields(self) -> list[str]:
        """
        获取Agent输出的状态字段

        Returns:
            list[str]: 输出字段列表
        """
        return []

    def pre_execute(self, state: Dict[str, Any]) -> None:
        """
        执行前的预处理钩子

        Args:
            state: 当前状态
        """
        self.logger.info(f"Agent {self.name} 开始执行")

    def post_execute(self, state: Dict[str, Any], result: AgentResult) -> None:
        """
        执行后的后处理钩子

        Args:
            state: 当前状态
            result: 执行结果
        """
        if result.success:
            self.logger.info(f"Agent {self.name} 执行成功")
        else:
            self.logger.error(f"Agent {self.name} 执行失败: {result.error}")

    def handle_error(self, error: Exception, state: Dict[str, Any]) -> AgentResult:
        """
        统一的错误处理

        Args:
            error: 异常对象
            state: 当前状态

        Returns:
            AgentResult: 错误结果
        """
        error_msg = f"Agent {self.name} 执行异常: {str(error)}"
        self.logger.error(error_msg, exc_info=True)

        return AgentResult.failed_result(
            error=error_msg,
            metadata={
                "exception_type": type(error).__name__,
                "agent_name": self.name
            }
        )

    def safe_execute(self, state: Dict[str, Any]) -> AgentResult:
        """
        安全执行Agent，包含完整的错误处理和生命周期管理

        Args:
            state: 当前状态

        Returns:
            AgentResult: 执行结果
        """
        try:
            # 前置验证
            if not self.validateInput(state):
                return AgentResult.failed_result(
                    error=f"Agent {self.name} 输入验证失败",
                    metadata={"required_fields": self.get_required_fields()}
                )

            # 执行前处理
            self.pre_execute(state)

            # 执行核心逻辑
            result = self.execute(state)

            # 执行后处理
            self.post_execute(state, result)

            return result

        except Exception as e:
            return self.handle_error(e, state)


class LLMAgent(BaseAgent):
    """
    基于LLM的Agent基类

    为需要使用大语言模型的Agent提供通用功能。
    """

    def __init__(self, name: str, llm=None):
        """
        初始化LLM Agent

        Args:
            name: Agent名称
            llm: LLM实例
        """
        super().__init__(name)
        self.llm = llm

    def setLLM(self, llm):
        """设置LLM实例"""
        self.llm = llm


class AgentRegistry:
    """
    Agent注册表

    管理所有Agent的注册和获取，支持动态注册和依赖注入。
    """

    def __init__(self):
        self._agents: Dict[str, BaseAgent] = {}
        self._agent_classes: Dict[str, type] = {}

    def register_agent(self, name: str, agent: BaseAgent) -> None:
        """
        注册Agent实例

        Args:
            name: Agent名称
            agent: Agent实例
        """
        self._agents[name] = agent
        self.logger.info(f"注册Agent: {name}")

    def register_agent_class(self, name: str, agent_class: type) -> None:
        """
        注册Agent类

        Args:
            name: Agent名称
            agent_class: Agent类
        """
        self._agent_classes[name] = agent_class

    def get_agent(self, name: str) -> BaseAgent:
        """
        获取Agent实例

        Args:
            name: Agent名称

        Returns:
            BaseAgent: Agent实例

        Raises:
            KeyError: Agent不存在时抛出
        """
        if name not in self._agents:
            raise KeyError(f"Agent '{name}' 未注册")
        return self._agents[name]

    def create_agent(self, name: str, **kwargs) -> BaseAgent:
        """
        创建Agent实例

        Args:
            name: Agent名称
            **kwargs: 构造参数

        Returns:
            BaseAgent: 新创建的Agent实例
        """
        if name not in self._agent_classes:
            raise KeyError(f"Agent类 '{name}' 未注册")

        agent_class = self._agent_classes[name]
        agent = agent_class(**kwargs)
        self.register_agent(name, agent)
        return agent

    def list_agents(self) -> list[str]:
        """
        列出所有已注册的Agent

        Returns:
            list[str]: Agent名称列表
        """
        return list(self._agents.keys())

    @property
    def logger(self):
        """获取日志记录器"""
        return loguru_logger


# 全局Agent注册表实例
agent_registry = AgentRegistry()
