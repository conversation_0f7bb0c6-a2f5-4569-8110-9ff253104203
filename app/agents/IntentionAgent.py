"""
意图识别Agent - 负责从用户输入中提取营销意图

作为营销工作流的核心组件，负责：
1. 分析用户输入的营销意图
2. 提取关键业务字段
3. 识别缺失信息
4. 生成补全建议
5. 输出标准化的意图信息
"""

import json
import time
import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from langchain_core.language_models import BaseLanguageModel
from langchain_core.messages import HumanMessage, SystemMessage

from app.agents.base import BaseAgent, AgentResult, agentLogDecorator
from app.utils.models import MarketingState, IntentInfo, MissingFields
from app.llm.LlmFactory import getDefaultLLM
from app.core.logging_config import getLogger
from app.agents.prompts.intention_prompts import IntentionPrompts
from app.utils.llm_utils import processLLMJsonResponse, cleanJsonResponse

logger = getLogger(__name__)

class IntentionAgent(BaseAgent):
    """
    意图识别Agent类
    
    主要职责：
    1. 从用户输入中提取营销意图
    2. 识别核心和次要业务字段
    3. 判断信息完整性
    4. 生成缺失字段建议
    5. 输出标准化的IntentInfo对象
    """

    def __init__(self, enableDetailedAnalysis: bool = True, 
                 enableFieldSuggestions: bool = True,
                 maxRetries: int = 3,
                 enableCache: bool = True,
                 guidanceStyle: str = "friendly"):
        """
        初始化意图识别Agent

        Args:
            enableDetailedAnalysis: 是否启用详细分析
            enableFieldSuggestions: 是否启用字段建议
            maxRetries: 最大重试次数
            enableCache: 是否启用缓存
            guidanceStyle: 引导话术风格 ("friendly", "professional", "casual", "detailed")
        """
        super().__init__("IntentionAgent")
        self.enableDetailedAnalysis = enableDetailedAnalysis
        self.enableFieldSuggestions = enableFieldSuggestions
        self.maxRetries = maxRetries
        self.enableCache = enableCache
        self.guidanceStyle = guidanceStyle
        self.llm = getDefaultLLM()
        
        # 定义核心字段和次要字段
        self.coreFields = ["goal", "audience", "budget"]
        self.secondaryFields = ["activityName", "period", "content", "incentive", "channels", "restriction"]
        
        # 简单缓存
        self._cache = {} if enableCache else None
        
        # 引导话术风格模板
        self.guidanceStyles = {
            "friendly": "语气友好自然，像朋友间的对话",
            "professional": "语气专业正式，体现专业性",
            "casual": "语气轻松随意，像聊天一样",
            "detailed": "语气详细具体，提供更多指导"
        }

    def validate_input(self, state: MarketingState) -> bool:
        """验证输入状态"""
        return state.get("userInput") is not None

    def get_required_fields(self) -> List[str]:
        """获取必需字段"""
        return ["userInput"]

    @agentLogDecorator
    def execute(self, state: MarketingState) -> MarketingState:
        """
        执行意图识别逻辑，分析缺失字段，若需补全则生成引导话术和建议

        Args:
            state: 当前工作流状态，包含用户输入及上下文信息
                - messages: 消息历史
                - userInput: 当前用户输入
                - userProfile: 用户画像信息
                - 其它业务相关状态

        Returns:
            MarketingState: 更新后的完整状态对象（MarketingState）

        主要流程：
            1. 获取最新用户输入
            2. 构建意图提取prompt
            3. 调用LLM抽取意图数据
            4. 标准化意图数据
            5. 分析缺失字段
            6. 判断是否需要补全
            7. 如需补全则生成引导话术和建议
            8. 更新并返回完整状态
        """
        try:
            messages = state["messages"]  # 这是 BaseMessage 列表

            # 获取最新的用户输入
            latestMessage = messages[-1] if messages else None
            userInput = latestMessage.content if latestMessage else state.get("userInput", "")

            # userInput = messages[-1].get("content") if messages else state.get("userInput", "")
            self.logger.info(f"意图识别Agent开始处理用户输入: {userInput[:50]}...")

            # 获取stream writer（在整个方法中使用）
            writer = None
            try:
                from langgraph.config import get_stream_writer
                writer = get_stream_writer()
            except Exception as e:
                self.logger.debug(f"获取stream writer失败: {e}")

            # 发送用户友好的进度消息
            try:
                if writer:
                    writer({
                        "type": "nodeProgress",
                        "nodeId": "INTENTION_AGENT",
                        "stage": "intent_analysis_start",
                        "content": "正在深入理解您的营销目标...",
                        "progress": 30
                    })
            except Exception as e:
                self.logger.debug(f"发送进度数据失败: {e}")

            # 构建意图提取提示词
            prompt = self._buildIntentExtractionPrompt(userInput, state.get("userProfile", {}))

            # 发送LLM调用进度
            try:
                if writer:
                    writer({
                        "type": "nodeProgress",
                        "nodeId": "INTENTION_AGENT",
                        "stage": "llm_processing",
                        "content": "正在分析活动类型和目标受众...",
                        "progress": 60
                    })
            except Exception as e:
                self.logger.debug(f"发送LLM进度失败: {e}")

            # 调用LLM抽取意图数据
            intentData = self._extractIntentWithRetry(prompt, userInput)
            # 标准化意图数据
            intentInfo = self._validateAndStandardizeIntent(intentData, userInput)

            # 发送用户友好的完成消息
            try:
                if writer:
                    activity_type = intentInfo.get('activityType', '营销活动')
                    goal = intentInfo.get('goal', '提升业务效果')
                    writer({
                        "type": "nodeProgress",
                        "nodeId": "INTENTION_AGENT",
                        "stage": "intent_analysis_complete",
                        "content": f"营销目标分析完成，识别为{activity_type}，目标是{goal}",
                        "progress": 100
                    })
            except Exception as e:
                self.logger.debug(f"发送意图结果失败: {e}")

            # 分析缺失字段
            missingFields = self._analyzeMissingFields(intentInfo)
            # 判断是否需要补全
            needCompletion = bool(missingFields.get('missingCoreFields', []))
            
            # 生成引导信息
            guidanceResult = {}
            if needCompletion:
                guidanceResult = self._generateGuidancePrompts(intentInfo, missingFields, userInput)
            
            # 更新状态
            state.update({
                "intentInfo": intentInfo,
                "needCompletion": needCompletion,
                "missingFields": missingFields,
                "guidancePrompts": guidanceResult.get("guidancePrompts", {}),
                "fieldSuggestions": guidanceResult.get("fieldSuggestions", {}),
                "followupQuestions": guidanceResult.get("followupQuestions", {})
            })
            self.logger.info(f"IntentionAgent 返回 state: {state}")
            return state
        except Exception as e:
            self.logger.error(f"意图识别Agent执行失败: {str(e)}")
            # 失败时也返回原始state，或可自定义错误处理
            return state

    def _buildIntentExtractionPrompt(self, userInput: str, inputAnalysis: Dict[str, Any]) -> str:
        """
        构建意图提取提示词 - 优化版本

        Args:
            userInput: 用户输入
            inputAnalysis: 输入分析结果

        Returns:
            str: 意图提取提示词
        """
        # 构建分析信息
        analysisInfo = ""
        if inputAnalysis:
            complexity = inputAnalysis.get("complexity", "unknown")
            keywords = inputAnalysis.get("foundKeywords", [])
            intentHints = inputAnalysis.get("intentHints", [])
            analysisInfo = f"""
输入分析信息：
- 复杂度: {complexity}
- 关键词: {', '.join(keywords) if keywords else '无'}
- 意图提示: {', '.join(intentHints) if intentHints else '无'}
"""

        return f"""
        你是一名专业的银行营销活动意图识别专家。请根据用户输入，精确提取营销活动的关键信息。

        【任务要求】
        1. 提取核心要素：活动目标(goal)、预算(budget)、目标客群(audience)
        2. 提取次要要素：活动名称、时间、内容、激励、渠道、限制
        3. 识别缺失字段并生成追问建议
        4. 提供字段建议值

        【思考过程】
        请在 <reasoning> 标签中以专业顾问的语气，用 Markdown 格式输出你的分析思考过程。要求：
        - 使用自然流畅的语言，像专家在和客户交流一样
        - 体现专业的营销策划思维
        - 展示逐步推理的过程
        - 语气要亲和且专业

        使用以下 Markdown 结构：
        - 使用 ## 二级标题组织思路
        - 语言要自然，避免机械化的列举
        - 使用 **粗体** 突出关键洞察
        - 体现思考的连贯性和逻辑性

        【Few-Shot示例】

        输入："我想做一个提升信用卡申请转化率的营销活动"

        <reasoning>
        ## 我来分析一下您的需求

        看到您想做一个**提升信用卡申请转化率**的活动，这个目标很明确！信用卡获客确实是我们银行营销的重点项目之一。

        ## 目前掌握的信息

        从您的描述中，我能明确提取到的是活动的核心目标。不过我发现还缺少一些关键信息，这对制定具体方案很重要：

        首先是**预算问题** - 这直接决定了我们能做多大规模的活动，是几万块的小试牛刀，还是几十万的大手笔投入？预算不同，我们的策略就会完全不一样。

        另外就是**目标客群** - 您是想拉新客户，还是激活老客户？是针对年轻白领，还是高净值人群？不同的人群，我们的触达方式和激励手段都要调整。

        ## 我的专业建议

        基于多年银行营销经验，信用卡推广活动通常需要考虑几个维度：活动周期、推广渠道、激励政策，还有风控限制等。这些细节虽然现在还没确定，但后续制定方案时都很关键。

        让我先整理一下现有信息，然后针对缺失的部分给您一些专业建议。
        </reasoning>

        输出：
        {{
          "goal": "提升信用卡申请转化率",
          "budget": "",
          "audience": "",
          "activityName": "",
          "period": "",
          "content": "",
          "incentive": "",
          "channels": "",
          "restriction": "",
          "missingCoreFields": ["budget", "audience"],
          "missingSecondaryFields": ["activityName", "period", "content", "incentive", "channels", "restriction"],
          "followupQuestions": {{
            "budget": "请问活动预算大概是多少？",
            "audience": "目标客群是什么？"
          }},
          "fieldSuggestions": {{
            "budget": ["5万元", "10万元", "20万元"],
            "audience": ["新客户", "沉睡客户", "受邀客户"]
          }}
        }}

        <result>
        ## 当前意图识别的活动安排：
            - 活动目标：提升信用卡申请转化率
            - 预算：xxx
            - 目标客群：xxx
            - 活动名称：xxx
            - 活动时间：xxx
            - 活动内容：xxx
            - 激励方式：xxx
            - 触达渠道：xxx
            - 参与限制：xxx
        </result>

        输入："目标客群是25-40岁上班族，预算10万元，通过支付宝绑卡送话费"

        <reasoning>
        ## 让我来梳理一下您提供的信息

        这次您提供的信息相当详细！**25-40岁上班族**作为目标客群很精准，这个年龄段正是信用卡的主力使用人群，有稳定收入且消费需求旺盛。

        **10万元预算**属于中等规模的营销投入，足够支撑一个不错的绑卡推广活动了。

        ## 我的专业分析

        从您描述的"支付宝绑卡送话费"来看，这是个很聪明的策略！利用**支付宝这个高频使用场景**，话费作为激励也很实用，基本上人人都需要。

        我推断您的主要目标应该是**提升绑卡率**，因为绑卡是后续消费激活的基础。通过支付宝渠道获客，成本相对可控，转化效果也比较好。
        </reasoning>

        输出：
        {{
          "goal": "提升绑卡率",
          "budget": "10万元",
          "audience": "25-40岁上班族",
          "activityName": "支付宝绑卡送话费活动",
          "period": "",
          "content": "通过支付宝绑卡送话费",
          "incentive": "话费",
          "channels": "支付宝",
          "restriction": "",
          "missingCoreFields": [],
          "missingSecondaryFields": ["period", "restriction"],
          "followupQuestions": {{
            "period": "活动时间是什么时候？",
            "restriction": "有什么参与限制吗？"
          }},
          "fieldSuggestions": {{
            "period": ["2024年Q3", "2024年Q4", "长期活动"],
            "restriction": ["每人限一次", "账户状态正常"]
          }}
        }}
        <result>
        ## 当前意图识别的活动安排：
            - 活动目标：提升绑卡率
            - 预算：10万元
            - 目标客群：25-40岁上班族
            - 活动名称：支付宝绑卡送话费活动
            - 活动时间：2024年Q3-2024年Q4
            - 活动内容：通过支付宝绑卡送话费
            - 激励方式：话费
            - 触达渠道：支付宝
            - 参与限制：每人限一次，账户状态正常
        </result>

        【当前输入】
        {userInput}

        {analysisInfo}

        【处理步骤】
        1. 首先在 <analysis> 标签中输出专业顾问式的分析过程
        2. 然后在 <result> 标签中按照 JSON 格式输出提取结果

        【输出要求】
        请严格按照以下格式输出：

        <reasoning>
        在这里输出你的专业分析思考过程，使用 Markdown 格式：
        - 使用 ## 三级标题组织思路
        - 语言要自然，像专家在和客户交流一样
        - 使用 **粗体** 突出关键洞察
        - 体现思考的连贯性和逻辑性
        </reasoning>

        然后直接输出JSON格式的提取结果（不要用代码块包裹）：
        {{
          "goal": "活动目标",
          "budget": "预算信息",
          "audience": "目标客群",
          "activityName": "活动名称",
          "period": "活动时间",
          "content": "活动内容",
          "incentive": "激励方式",
          "channels": "触达渠道",
          "restriction": "参与限制",
          "missingCoreFields": ["缺失的核心字段"],
          "missingSecondaryFields": ["缺失的次要字段"],
          "followupQuestions": {{"字段": "追问问题"}},
          "fieldSuggestions": {{"字段": ["建议值1", "建议值2", "建议值3"]}}
        }}
        <result>
        意图识别的结果
        </result>
        注意事项：
        - reasoning标签内只包含思考过程，不包含JSON数据
        - JSON数据在reasoning标签外单独输出，不要用```json包裹
        - 确保JSON格式正确且字段完整
        """

    def _extractIntentWithRetry(self, prompt: str, userInput: str) -> Dict[str, Any]:
        """
        使用LLM提取意图信息 - 带重试和缓存

        Args:
            prompt: 意图提取提示词
            userInput: 用户输入

        Returns:
            Dict[str, Any]: 意图数据
        """
        try:
            if not self.llm:
                raise ValueError("LLM实例未设置")

            # 检查缓存
            if self._cache and prompt in self._cache:
                self.logger.debug("从缓存中获取意图数据")
                return self._cache[prompt]

            for _ in range(self.maxRetries):
                response = self.llm.invoke(prompt)
                responseText = response.content if hasattr(response, 'content') else str(response)

                # 验证响应内容
                if not responseText or responseText.strip() == "":
                    self.logger.warning("LLM返回空响应，尝试重试")
                    continue

                # 记录原始响应用于调试
                self.logger.debug(f"LLM原始响应: {responseText[:20000]}...")

                # 尝试解析JSON
                try:
                    # 提取JSON部分（跳过reasoning标签）
                    json_content = self._extractJsonFromResponse(responseText)

                    intentData = json.loads(json_content)
                    self.logger.debug(f"LLM意图提取成功，响应长度: {len(responseText)}")

                    # 验证返回的数据结构
                    if not isinstance(intentData, dict):
                        raise ValueError("LLM返回的不是有效的字典格式")

                    # 评估提取质量
                    confidence = self._evaluateExtractionConfidence(intentData, responseText)
                    intentData["extractionConfidence"] = confidence

                    self.logger.info(f"意图提取置信度: {confidence:.2f}")

                    # 更新缓存
                    if self._cache:
                        self._cache[prompt] = intentData

                    return intentData
                except json.JSONDecodeError as e:
                    self.logger.error(f"LLM输出无法解析为JSON: {e}")
                    self.logger.debug(f"无法解析的响应内容: {responseText}")
                    # 继续重试
                    continue
                except ValueError as e:
                    self.logger.error(f"LLM返回数据格式错误: {str(e)}")
                    # 继续重试
                    continue

        except Exception as e:
            self.logger.error(f"LLM意图提取失败: {str(e)}")
            return self._getDefaultIntentData("")

    def _evaluateExtractionConfidence(self, intentData: Dict[str, Any], rawResponse: str) -> float:
        """
        评估意图提取的置信度

        Args:
            intentData: 提取的意图数据
            rawResponse: 原始LLM响应

        Returns:
            float: 置信度分数 (0.0-1.0)
        """
        confidence = 0.5  # 基础置信度

        # 1. 核心字段完整性评估
        coreFields = ["goal", "budget", "audience"]
        filledCoreFields = sum(1 for field in coreFields if intentData.get(field, "").strip())
        coreCompleteness = filledCoreFields / len(coreFields)
        confidence += coreCompleteness * 0.3

        # 2. 次要字段丰富度评估
        secondaryFields = ["activityName", "period", "content", "incentive", "channels", "restriction"]
        filledSecondaryFields = sum(1 for field in secondaryFields if intentData.get(field, "").strip())
        secondaryRichness = min(filledSecondaryFields / len(secondaryFields), 1.0)
        confidence += secondaryRichness * 0.2

        # 3. 响应质量评估
        if len(rawResponse) > 100:  # 响应长度合理
            confidence += 0.1

        # 4. 字段建议质量评估
        fieldSuggestions = intentData.get("fieldSuggestions", {})
        if fieldSuggestions and any(len(suggestions) > 0 for suggestions in fieldSuggestions.values()):
            confidence += 0.1

        return min(confidence, 1.0)

    def _getDefaultIntentData(self, rawResponse: str) -> Dict[str, Any]:
        """
        获取默认意图数据结构

        Args:
            rawResponse: 原始LLM响应

        Returns:
            Dict[str, Any]: 默认意图数据
        """
        return {
            "goal": "",
            "budget": "",
            "audience": "",
            "activityName": "",
            "period": "",
            "content": "",
            "incentive": "",
            "channels": "",
            "restriction": "",
            "missingCoreFields": self.coreFields,
            "missingSecondaryFields": self.secondaryFields,
            "followupQuestions": {},
            "fieldSuggestions": {},
            "rawResponse": rawResponse
        }

    def _validateAndStandardizeIntent(self, intentData: Dict[str, Any], userInput: str) -> IntentInfo:
        """
        验证和标准化意图数据 - 增强版本

        Args:
            intentData: 原始意图数据
            userInput: 用户输入

        Returns:
            IntentInfo: 标准化的意图信息
        """
        try:
            # 提取核心字段
            goal = intentData.get("goal", "").strip()
            budget = intentData.get("budget", "").strip()
            audience = intentData.get("audience", "").strip()

            # 如果没有提取到目标，使用用户输入作为默认值
            if not goal:
                goal = userInput

            # 业务规则验证
            validationResult = self._validateBusinessRules(goal, budget, audience)
            if not validationResult["valid"]:
                self.logger.warning(f"业务规则验证失败: {validationResult['issues']}")

            # 创建IntentInfo对象
            intentInfo = IntentInfo(
                bankCode="DEFAULT",  # 默认值，后续可更新
                bankName="默认银行",  # 默认值，后续可更新
                goal=goal,
                activityType="营销活动",  # 默认值
                roi=budget,  # 将预算作为ROI
                audience=audience,
                extra={
                    "activityName": intentData.get("activityName", ""),
                    "period": intentData.get("period", ""),
                    "content": intentData.get("content", ""),
                    "incentive": intentData.get("incentive", ""),
                    "channels": intentData.get("channels", ""),
                    "restriction": intentData.get("restriction", ""),
                    "extractionTime": datetime.now().isoformat(),
                    "validationResult": validationResult
                }
            )

            return intentInfo

        except Exception as e:
            self.logger.error(f"意图数据标准化失败: {str(e)}")
            # 返回最小化的IntentInfo
            return IntentInfo(
                bankCode="DEFAULT",
                bankName="默认银行",
                goal=userInput,
                activityType="营销活动",
                extra={"error": str(e)}
            )

    def _validateBusinessRules(self, goal: str, budget: str, audience: str) -> Dict[str, Any]:
        """
        验证业务规则

        Args:
            goal: 活动目标
            budget: 预算
            audience: 目标客群

        Returns:
            Dict[str, Any]: 验证结果
        """
        issues = []
        warnings = []

        # 1. 目标验证
        if goal:
            if len(goal) < 5:
                issues.append("活动目标描述过短，建议更详细")
            if "提升" not in goal and "增加" not in goal and "促进" not in goal:
                warnings.append("活动目标建议包含'提升'、'增加'或'促进'等动词")

        # 2. 预算验证
        if budget:
            if "万" in budget or "元" in budget:
                try:
                    # 简单的数字提取
                    import re
                    numbers = re.findall(r'\d+', budget)
                    if numbers:
                        amount = int(numbers[0])
                        if amount < 1:
                            issues.append("预算金额过小，请确认")
                        elif amount > 1000:
                            warnings.append("预算金额较大，请确认")
                except:
                    warnings.append("预算格式不规范，建议使用'X万元'格式")
            else:
                warnings.append("预算格式建议使用'X万元'或'X元'")

        # 3. 客群验证
        if audience:
            if len(audience) < 3:
                issues.append("目标客群描述过短，建议更详细")
            if "客户" not in audience and "用户" not in audience and "群体" not in audience:
                warnings.append("目标客群建议包含'客户'、'用户'或'群体'等关键词")

        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "warnings": warnings,
            "score": max(0, 1.0 - len(issues) * 0.2 - len(warnings) * 0.1)
        }

    def _analyzeMissingFields(self, intentInfo: IntentInfo) -> MissingFields:
        """
        分析缺失字段

        Args:
            intentInfo: 意图信息

        Returns:
            MissingFields: 缺失字段信息
        """
        missingCoreFields = []
        missingSecondaryFields = []
        fieldDescriptions = {}
        priority = {}

        # 检查核心字段
        if not intentInfo.get('goal') or intentInfo.get('goal', '').strip() == "":
            missingCoreFields.append("goal")
            fieldDescriptions["goal"] = "营销活动的主要目标"
            priority["goal"] = 1

        if not intentInfo.get('audience') or intentInfo.get('audience', '').strip() == "":
            missingCoreFields.append("audience")
            fieldDescriptions["audience"] = "目标客户群体"
            priority["audience"] = 2

        if not intentInfo.get('roi') or intentInfo.get('roi', '').strip() == "":
            missingCoreFields.append("budget")
            fieldDescriptions["budget"] = "活动预算或ROI要求"
            priority["budget"] = 3

        # 检查次要字段
        extra = intentInfo.get('extra', {}) or {}
        if not extra.get("activityName"):
            missingSecondaryFields.append("activityName")
            fieldDescriptions["activityName"] = "活动名称"
            priority["activityName"] = 4

        if not extra.get("period"):
            missingSecondaryFields.append("period")
            fieldDescriptions["period"] = "活动时间周期"
            priority["period"] = 5

        if not extra.get("content"):
            missingSecondaryFields.append("content")
            fieldDescriptions["content"] = "活动内容和规则"
            priority["content"] = 6

        if not extra.get("incentive"):
            missingSecondaryFields.append("incentive")
            fieldDescriptions["incentive"] = "激励方式"
            priority["incentive"] = 7

        if not extra.get("channels"):
            missingSecondaryFields.append("channels")
            fieldDescriptions["channels"] = "触达渠道"
            priority["channels"] = 8

        if not extra.get("restriction"):
            missingSecondaryFields.append("restriction")
            fieldDescriptions["restriction"] = "参与限制"
            priority["restriction"] = 9

        return MissingFields(
            missingCoreFields=missingCoreFields,
            missingSecondaryFields=missingSecondaryFields,
            fieldDescriptions=fieldDescriptions,
            priority=priority
        )

    def _generateGuidancePrompts(self, intentInfo: IntentInfo, missingFields: MissingFields, userInput: str) -> Dict[str, Any]:
        """
        生成全面的通用引导话术和字段建议 - 使用LLM智能生成

        Args:
            intentInfo: 意图信息
            missingFields: 缺失字段信息
            userInput: 用户输入

        Returns:
            Dict[str, Any]: 包含guidancePrompts、fieldSuggestions、followupQuestions的字典
        """
        if not missingFields.get('missingCoreFields', []) and not missingFields.get('missingSecondaryFields', []):
            return {
                "guidancePrompts": {},
                "fieldSuggestions": {},
                "followupQuestions": {}
            }

        try:
            # 构建全面的引导话术生成提示词
            prompt = self._buildComprehensiveGuidancePrompt(intentInfo, missingFields, userInput)

            # 调用LLM生成全面的引导话术
            response = self.llm.invoke(prompt)
            responseText = response.content if hasattr(response, 'content') else str(response)

            # 验证响应内容
            if not responseText or responseText.strip() == "":
                self.logger.warning("LLM返回空响应，使用默认话术")
                return self._getDefaultComprehensiveGuidance(intentInfo, missingFields)

            # 记录原始响应用于调试
            self.logger.debug(f"LLM原始响应: {responseText[:200]}...")

            # 尝试解析JSON
            try:
                # 清理响应文本，移除可能的Markdown代码块标记
                cleaned_response = responseText.strip()
                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response[7:]  # 移除 ```json
                if cleaned_response.startswith("```"):
                    cleaned_response = cleaned_response[3:]  # 移除 ```
                if cleaned_response.endswith("```"):
                    cleaned_response = cleaned_response[:-3]  # 移除结尾的 ```

                cleaned_response = cleaned_response.strip()

                guidanceData = json.loads(cleaned_response)
                self.logger.debug(f"全面的引导话术生成成功，缺失字段数: {len(missingFields.get('missingCoreFields', [])) + len(missingFields.get('missingSecondaryFields', []))}")

                # 验证返回的数据结构
                if not isinstance(guidanceData, dict):
                    raise ValueError("LLM返回的不是有效的字典格式")

                return {
                    "guidancePrompts": guidanceData.get("guidancePrompts", {}),
                    "fieldSuggestions": guidanceData.get("fieldSuggestions", {}),
                    "followupQuestions": guidanceData.get("followupQuestions", {})
                }

            except json.JSONDecodeError as e:
                self.logger.warning(f"LLM全面的引导话术解析失败: {str(e)}")
                self.logger.debug(f"无法解析的响应内容: {responseText}")
                return self._getDefaultComprehensiveGuidance(intentInfo, missingFields)

            except ValueError as e:
                self.logger.warning(f"LLM返回数据格式错误: {str(e)}")
                return self._getDefaultComprehensiveGuidance(intentInfo, missingFields)

        except Exception as e:
            self.logger.error(f"全面的引导话术生成失败: {str(e)}")
            return self._getDefaultComprehensiveGuidance(intentInfo, missingFields)

    def _validateGuidanceCompleteness(self, guidanceData: Dict[str, str], missingFields: MissingFields) -> Dict[str, str]:
        """
        验证引导话术的完整性，确保所有缺失字段都有对应的引导话术

        Args:
            guidanceData: LLM生成的引导话术
            missingFields: 缺失字段信息

        Returns:
            Dict[str, str]: 完整的引导话术
        """
        validatedGuidance = guidanceData.copy()

        # 检查核心字段
        for field in missingFields.get('missingCoreFields', []):
            if field not in validatedGuidance or not validatedGuidance[field].strip():
                self.logger.warning(f"核心字段 {field} 缺少引导话术，使用默认话术")
                validatedGuidance[field] = self._getDefaultPromptForField(field, "core")

        # 检查次要字段
        for field in missingFields.get('missingSecondaryFields', []):
            if field not in validatedGuidance or not validatedGuidance[field].strip():
                self.logger.warning(f"次要字段 {field} 缺少引导话术，使用默认话术")
                validatedGuidance[field] = self._getDefaultPromptForField(field, "secondary")

        return validatedGuidance

    def _getDefaultPromptForField(self, field: str, fieldType: str) -> str:
        """
        获取字段的默认引导话术

        Args:
            field: 字段名
            fieldType: 字段类型 ("core" 或 "secondary")

        Returns:
            str: 默认引导话术
        """
        defaultPrompts = {
            "goal": "请告诉我你希望这个营销活动达到什么目标？比如提升转化率、增加用户活跃度等。",
            "audience": "请描述一下你的目标客群，比如年龄段、用户类型等。",
            "budget": "请告诉我活动的预算大概是多少？这样我可以为你设计更合适的方案。",
            "activityName": "请为这个活动起个名字，让用户更容易记住。",
            "period": "请告诉我活动的时间安排，比如具体的时间段或持续时间。",
            "content": "请描述一下活动的具体内容和规则。",
            "incentive": "请告诉我活动的激励方式，比如奖品、优惠等。",
            "channels": "请告诉我你希望通过哪些渠道触达用户，比如短信、APP推送等。",
            "restriction": "请告诉我活动的参与限制，比如每人限参与一次等。"
        }

        return defaultPrompts.get(field, f"请补充{field}相关信息。")

    def _buildComprehensiveGuidancePrompt(self, intentInfo: IntentInfo, missingFields: MissingFields, userInput: str) -> str:
        """
        构建全面的引导话术生成提示词

        Args:
            intentInfo: 意图信息
            missingFields: 缺失字段信息
            userInput: 用户输入

        Returns:
            str: 全面引导话术生成提示词
        """
        # 构建当前状态信息
        currentState = {
            "goal": intentInfo.get('goal') or "未提供",
            "audience": intentInfo.get('audience') or "未提供",
            "budget": intentInfo.get('roi') or "未提供",
            "activityName": intentInfo.get('extra', {}).get('activityName', "未提供"),
            "period": intentInfo.get('extra', {}).get('period', "未提供"),
            "content": intentInfo.get('extra', {}).get('content', "未提供"),
            "incentive": intentInfo.get('extra', {}).get('incentive', "未提供"),
            "channels": intentInfo.get('extra', {}).get('channels', "未提供"),
            "restriction": intentInfo.get('extra', {}).get('restriction', "未提供")
        }

        missingCore = missingFields.get('missingCoreFields', [])
        missingSecondary = missingFields.get('missingSecondaryFields', [])
        totalMissing = len(missingCore) + len(missingSecondary)

        # 分析用户输入特征
        inputFeatures = self._analyzeInputFeatures(userInput)

        # 获取风格描述
        styleDesc = self.guidanceStyles.get(self.guidanceStyle, "语气友好自然")

        return f"""
你是一名专业的营销顾问，需要为用户生成一个全面的引导话术，帮助他们完善营销活动信息。

【用户原始输入】
{userInput}

【用户输入特征分析】
- 输入长度: {inputFeatures['length']} 字符
- 复杂度: {inputFeatures['complexity']}
- 关键词: {', '.join(inputFeatures['keywords'])}
- 意图类型: {inputFeatures['intentType']}

【当前已提取的信息】
- 活动目标: {currentState['goal']}
- 目标客群: {currentState['audience']}
- 预算: {currentState['budget']}
- 活动名称: {currentState['activityName']}
- 活动时间: {currentState['period']}
- 活动内容: {currentState['content']}
- 激励方式: {currentState['incentive']}
- 触达渠道: {currentState['channels']}
- 参与限制: {currentState['restriction']}

【缺失字段统计】
- 核心字段缺失: {len(missingCore)} 个 ({', '.join(missingCore) if missingCore else '无'})
- 次要字段缺失: {len(missingSecondary)} 个 ({', '.join(missingSecondary) if missingSecondary else '无'})
- 总计缺失: {totalMissing} 个字段

【任务要求】
请生成一个全面的、通用的引导话术，要求：
1. {styleDesc}
2. 不要为每个字段单独生成话术，而是生成一个整体的引导话术
3. 引导用户一次性补充所有缺失的信息
4. 提供具体的示例和建议
5. 根据缺失字段的数量调整话术策略
6. 考虑用户输入特征，提供个性化的建议

【输出格式】
请严格按照JSON格式输出：
{{
  "guidancePrompts": {{
    "comprehensive": "全面的引导话术，引导用户补充所有缺失信息"
  }},
  "fieldSuggestions": {{
    "goal": ["建议值1", "建议值2", "建议值3"],
    "audience": ["建议值1", "建议值2", "建议值3"],
    "budget": ["建议值1", "建议值2", "建议值3"]
  }},
  "followupQuestions": {{
    "comprehensive": "整体的追问问题，引导用户提供完整信息"
  }}
}}

请只为缺失的字段提供建议值，不要包含已完善的字段。
"""

    def _analyzeInputFeatures(self, userInput: str) -> Dict[str, Any]:
        """
        分析用户输入特征

        Args:
            userInput: 用户输入

        Returns:
            Dict[str, Any]: 输入特征
        """
        # 基本特征
        length = len(userInput)

        # 复杂度判断
        if length < 20:
            complexity = "simple"
        elif length < 50:
            complexity = "medium"
        else:
            complexity = "detailed"

        # 关键词提取
        keywords = []
        marketingKeywords = ["营销", "活动", "推广", "促销", "转化", "绑卡", "申请", "客户", "用户"]
        for keyword in marketingKeywords:
            if keyword in userInput:
                keywords.append(keyword)

        # 意图类型判断
        intentType = "general"
        if any(kw in userInput for kw in ["提升", "增加", "促进"]):
            intentType = "improvement"
        elif any(kw in userInput for kw in ["拉新", "获客", "新客户"]):
            intentType = "acquisition"
        elif any(kw in userInput for kw in ["留存", "激活", "唤醒"]):
            intentType = "retention"

        return {
            "length": length,
            "complexity": complexity,
            "keywords": keywords,
            "intentType": intentType
        }

    def _getDefaultGuidancePrompts(self, intentInfo: IntentInfo, missingFields: MissingFields) -> Dict[str, str]:
        """
        获取默认引导话术（兜底方案）

        Args:
            intentInfo: 意图信息
            missingFields: 缺失字段信息

        Returns:
            Dict[str, str]: 默认引导话术
        """
        prompts = {}

        # 核心字段的默认引导话术
        if "goal" in missingFields.get('missingCoreFields', []):
            prompts["goal"] = "请告诉我你希望这个营销活动达到什么目标？比如提升转化率、增加用户活跃度等。"

        if "audience" in missingFields.get('missingCoreFields', []):
            prompts["audience"] = "请描述一下你的目标客群，比如年龄段、用户类型等。"

        if "budget" in missingFields.get('missingCoreFields', []):
            prompts["budget"] = "请告诉我活动的预算大概是多少？这样我可以为你设计更合适的方案。"

        # 次要字段的默认引导话术
        if "activityName" in missingFields.get('missingSecondaryFields', []):
            prompts["activityName"] = "请为这个活动起个名字，让用户更容易记住。"

        if "period" in missingFields.get('missingSecondaryFields', []):
            prompts["period"] = "请告诉我活动的时间安排，比如具体的时间段或持续时间。"

        if "content" in missingFields.get('missingSecondaryFields', []):
            prompts["content"] = "请描述一下活动的具体内容和规则。"

        if "incentive" in missingFields.get('missingSecondaryFields', []):
            prompts["incentive"] = "请告诉我活动的激励方式，比如奖品、优惠等。"

        if "channels" in missingFields.get('missingSecondaryFields', []):
            prompts["channels"] = "请告诉我你希望通过哪些渠道触达用户，比如短信、APP推送等。"

        if "restriction" in missingFields.get('missingSecondaryFields', []):
            prompts["restriction"] = "请告诉我活动的参与限制，比如每人限参与一次等。"

        return prompts

    def _assessIntentQuality(self, intentInfo: IntentInfo, missingFields: MissingFields) -> Dict[str, Any]:
        """
        评估意图质量并生成优化建议

        Args:
            intentInfo: 意图信息
            missingFields: 缺失字段信息

        Returns:
            Dict[str, Any]: 意图质量评估结果
        """
        # 实现意图质量评估逻辑
        # 这里可以根据实际需求实现不同的评估方法
        return {
            "qualityScore": 0.8,  # 示例：占位符，实际实现需要根据业务逻辑
            "optimizationSuggestions": "根据意图质量评估结果生成优化建议"
        }

    def _getCurrentTimestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().isoformat()

    def _extractFieldSuggestionsFromGuidance(self, guidanceData: Dict[str, str], missingFields: MissingFields) -> Dict[str, List[str]]:
        """
        从引导话术中提取字段建议

        Args:
            guidanceData: 引导话术数据
            missingFields: 缺失字段信息

        Returns:
            Dict[str, List[str]]: 字段建议
        """
        fieldSuggestions = {}

        # 为每个缺失字段生成默认建议
        for field in missingFields.get('missingCoreFields', []) + missingFields.get('missingSecondaryFields', []):
            if field == "goal":
                fieldSuggestions[field] = ["提升转化率", "增加用户活跃度", "促进绑卡", "提升申请量"]
            elif field == "audience":
                fieldSuggestions[field] = ["新客户", "沉睡客户", "受邀客户", "25-40岁上班族"]
            elif field == "budget":
                fieldSuggestions[field] = ["5万元", "10万元", "20万元", "50万元"]
            elif field == "activityName":
                fieldSuggestions[field] = ["绑卡送话费", "申请有礼", "激活奖励", "新户专享"]
            elif field == "period":
                fieldSuggestions[field] = ["2024年Q3", "2024年Q4", "长期活动", "限时活动"]
            elif field == "content":
                fieldSuggestions[field] = ["绑卡即送话费", "申请成功有奖励", "激活账户送好礼"]
            elif field == "incentive":
                fieldSuggestions[field] = ["话费", "积分", "现金红包", "实物奖品"]
            elif field == "channels":
                fieldSuggestions[field] = ["短信", "APP推送", "微信", "支付宝"]
            elif field == "restriction":
                fieldSuggestions[field] = ["每人限一次", "账户状态正常", "新户专享", "无限制"]

        return fieldSuggestions

    def _extractFollowupQuestionsFromGuidance(self, guidanceData: Dict[str, str], missingFields: MissingFields) -> Dict[str, str]:
        """
        从引导话术中提取追问问题

        Args:
            guidanceData: 引导话术数据
            missingFields: 缺失字段信息

        Returns:
            Dict[str, str]: 追问问题
        """
        followupQuestions = {}

        # 直接使用引导话术作为追问问题，或者生成简化版本
        for field in missingFields.get('missingCoreFields', []) + missingFields.get('missingSecondaryFields', []):
            if field in guidanceData:
                # 简化引导话术，提取核心问题
                guidance = guidanceData[field]
                # 移除示例部分，保留核心问题
                if "比如" in guidance:
                    question = guidance.split("比如")[0].strip()
                elif "例如" in guidance:
                    question = guidance.split("例如")[0].strip()
                else:
                    question = guidance
                followupQuestions[field] = question
            else:
                # 生成默认追问问题
                followupQuestions[field] = self._getDefaultPromptForField(field, "core")

        return followupQuestions

    def _getDefaultFieldSuggestions(self, missingFields: MissingFields) -> Dict[str, List[str]]:
        """
        获取默认字段建议

        Args:
            missingFields: 缺失字段信息

        Returns:
            Dict[str, List[str]]: 默认字段建议
        """
        return self._extractFieldSuggestionsFromGuidance({}, missingFields)

    def _getDefaultFollowupQuestions(self, missingFields: MissingFields) -> Dict[str, str]:
        """
        获取默认追问问题

        Args:
            missingFields: 缺失字段信息

        Returns:
            Dict[str, str]: 默认追问问题
        """
        followupQuestions = {}
        for field in missingFields.get('missingCoreFields', []) + missingFields.get('missingSecondaryFields', []):
            followupQuestions[field] = self._getDefaultPromptForField(field, "core")
        return followupQuestions

    def _getDefaultComprehensiveGuidance(self, intentInfo: IntentInfo, missingFields: MissingFields) -> Dict[str, Any]:
        """
        获取默认的全面引导话术

        Args:
            intentInfo: 意图信息
            missingFields: 缺失字段信息

        Returns:
            Dict[str, Any]: 默认的全面引导话术
        """
        missingCore = missingFields.get('missingCoreFields', [])
        missingSecondary = missingFields.get('missingSecondaryFields', [])
        totalMissing = len(missingCore) + len(missingSecondary)

        # 生成全面的引导话术
        if totalMissing > 3:
            comprehensivePrompt = f"我看到您的营销活动还需要补充一些重要信息。为了给您设计更精准的方案，请告诉我：{', '.join(missingCore[:2])}等关键信息。您可以参考我提供的建议值，或者直接告诉我您的想法。"
        elif totalMissing > 1:
            comprehensivePrompt = f"您的营销活动还需要完善一些细节。请补充：{', '.join(missingCore + missingSecondary)}等信息，这样我可以为您制定更完整的方案。"
        else:
            field = (missingCore + missingSecondary)[0] if (missingCore + missingSecondary) else "相关信息"
            comprehensivePrompt = f"请补充{field}，这样我可以为您完善营销方案。"

        # 生成字段建议
        fieldSuggestions = {}
        for field in missingCore + missingSecondary:
            if field == "goal":
                fieldSuggestions[field] = ["提升转化率", "增加用户活跃度", "促进绑卡", "提升申请量"]
            elif field == "audience":
                fieldSuggestions[field] = ["新客户", "沉睡客户", "受邀客户", "25-40岁上班族"]
            elif field == "budget":
                fieldSuggestions[field] = ["5万元", "10万元", "20万元", "50万元"]
            elif field == "activityName":
                fieldSuggestions[field] = ["绑卡送话费", "申请有礼", "激活奖励", "新户专享"]
            elif field == "period":
                fieldSuggestions[field] = ["2024年Q3", "2024年Q4", "长期活动", "限时活动"]
            elif field == "content":
                fieldSuggestions[field] = ["绑卡即送话费", "申请成功有奖励", "激活账户送好礼"]
            elif field == "incentive":
                fieldSuggestions[field] = ["话费", "积分", "现金红包", "实物奖品"]
            elif field == "channels":
                fieldSuggestions[field] = ["短信", "APP推送", "微信", "支付宝"]
            elif field == "restriction":
                fieldSuggestions[field] = ["每人限一次", "账户状态正常", "新户专享", "无限制"]

        # 生成追问问题
        followupQuestions = {
            "comprehensive": f"请补充以下信息：{', '.join(missingCore + missingSecondary)}"
        }

        return {
            "guidancePrompts": {"comprehensive": comprehensivePrompt},
            "fieldSuggestions": fieldSuggestions,
            "followupQuestions": followupQuestions
        }

    def _extract_json_from_response(self, response_text: str) -> Dict[str, Any]:
        """
        智能提取JSON内容，支持多种LLM响应格式

        Args:
            response_text: LLM的原始响应文本

        Returns:
            解析后的JSON字典

        Raises:
            ValueError: 无法提取有效的JSON内容
        """
        if not response_text or not response_text.strip():
            raise ValueError("响应文本为空")

        # 1. 尝试直接解析（最简单的情况）
        try:
            return json.loads(response_text.strip())
        except json.JSONDecodeError:
            pass

        # 2. 使用正则表达式查找JSON内容
        # 匹配 ```json ... ``` 或 ``` ... ``` 格式
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # ```json {content} ```
            r'```\s*(\{.*?\})\s*```',      # ``` {content} ```
            r'```json\s*(\[.*?\])\s*```',  # ```json [content] ```
            r'```\s*(\[.*?\])\s*```',      # ``` [content] ```
        ]

        for pattern in json_patterns:
            match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
            if match:
                try:
                    return json.loads(match.group(1).strip())
                except json.JSONDecodeError:
                    continue

        # 3. 查找最外层的花括号或方括号
        # 找到第一个 { 或 [ 和最后一个 } 或 ]
        text = response_text.strip()

        # 查找第一个 { 或 [
        start_chars = ['{', '[']
        start_pos = -1
        start_char = None

        for char in start_chars:
            pos = text.find(char)
            if pos != -1 and (start_pos == -1 or pos < start_pos):
                start_pos = pos
                start_char = char

        if start_pos == -1:
            raise ValueError("未找到JSON开始标记")

        # 查找匹配的结束标记
        end_char = '}' if start_char == '{' else ']'
        bracket_count = 0
        end_pos = -1

        for i, char in enumerate(text[start_pos:], start_pos):
            if char == start_char:
                bracket_count += 1
            elif char == end_char:
                bracket_count -= 1
                if bracket_count == 0:
                    end_pos = i
                    break

        if end_pos == -1:
            raise ValueError("未找到匹配的JSON结束标记")

        # 提取JSON内容
        json_content = text[start_pos:end_pos + 1]

        try:
            return json.loads(json_content)
        except json.JSONDecodeError as e:
            raise ValueError(f"提取的JSON内容无效: {e}")

    def _build_intent_analysis_prompt(self, user_input: str) -> str:
        """构建意图分析提示词"""
        return f"""你是一个专业的营销意图分析专家。请分析用户的输入，提取营销意图和相关信息。

用户输入: {user_input}

请以JSON格式返回分析结果，包含以下字段：
{{
    "intentType": "营销意图类型，如：产品推广、客户分析、活动策划、内容营销等",
    "confidence": "置信度，0-1之间的数字",
    "extractedInfo": {{
        "productInfo": "产品相关信息",
        "targetAudience": "目标受众信息", 
        "marketingGoal": "营销目标",
        "budget": "预算信息",
        "timeline": "时间安排",
        "channel": "营销渠道",
        "content": "内容要求"
    }},
    "missingCoreFields": ["缺失的核心字段列表"],
    "missingSecondaryFields": ["缺失的次要字段列表"],
    "fieldSuggestions": "针对缺失字段的改进建议和指导",
    "followupQuestions": ["需要进一步询问的问题列表"]
}}

注意：
1. 如果某个字段在用户输入中未提及，请在extractedInfo中设为null
2. missingCoreFields包含必须的核心信息，如目标受众、营销目标等
3. missingSecondaryFields包含有助于优化的次要信息
4. fieldSuggestions应该提供具体、可操作的指导
5. followupQuestions应该针对缺失的核心信息提出具体问题

请确保返回的是有效的JSON格式，不要包含任何额外的文本说明。"""

    def _build_guidance_prompt(self, user_input: str, missing_fields: List[str], intent_type: str) -> str:
        """构建个性化指导提示词"""
        return f"""你是一个专业的营销顾问。用户正在制定营销计划，但缺少一些重要信息。

用户输入: {user_input}
营销意图: {intent_type}
缺失字段: {', '.join(missing_fields)}

请提供一份综合性的指导建议，帮助用户完善营销计划。指导应该：
1. 针对具体的缺失字段提供实用建议
2. 考虑用户的营销意图和已有信息
3. 提供具体的示例和可操作的建议
4. 语言友好、专业且易于理解

请直接返回指导内容，不要包含JSON格式或其他标记。"""

    def analyze_intent(self, user_input: str) -> Tuple[IntentInfo, MissingFields]:
        """
        分析用户输入的营销意图

        Args:
            user_input: 用户输入文本

        Returns:
            (IntentInfo, MissingFields): 意图信息和缺失字段信息
        """
        try:
            # 构建提示词
            prompt = self._build_intent_analysis_prompt(user_input)

            # 调用LLM
            messages = [
                SystemMessage(content="你是一个专业的营销意图分析专家，请严格按照JSON格式返回分析结果。"),
                HumanMessage(content=prompt)
            ]

            response = self.llm.invoke(messages)
            response_text = response.content if hasattr(response, 'content') else str(response)

            self.logger.info(f"LLM原始响应: {response_text[:200]}...")

            # 智能提取JSON
            intent_data = self._extract_json_from_response(response_text)

            # 构建IntentInfo
            intent_info = IntentInfo(
                intentType=intent_data.get("intentType", "未知"),
                confidence=float(intent_data.get("confidence", 0.5)),
                extractedInfo=intent_data.get("extractedInfo", {})
            )

            # 构建MissingFields
            missing_fields = MissingFields(
                missingCoreFields=intent_data.get("missingCoreFields", []),
                missingSecondaryFields=intent_data.get("missingSecondaryFields", []),
                fieldSuggestions=intent_data.get("fieldSuggestions", ""),
                followupQuestions=intent_data.get("followupQuestions", [])
            )

            self.logger.info(f"意图分析完成: {intent_info.intentType}, 置信度: {intent_info.confidence}")
            self.logger.info(f"缺失核心字段: {missing_fields.missingCoreFields}")

            return intent_info, missing_fields

        except Exception as e:
            self.logger.error(f"意图分析失败: {e}")
            # 返回默认值
            default_intent = IntentInfo(
                intentType="未知",
                confidence=0.0,
                extractedInfo={}
            )
            default_missing = MissingFields(
                missingCoreFields=["目标受众", "营销目标"],
                missingSecondaryFields=["预算", "时间安排"],
                fieldSuggestions="请提供更多关于目标受众和营销目标的信息，以便制定更精准的营销计划。",
                followupQuestions=["您的目标受众是谁？", "您希望达到什么营销目标？"]
            )
            return default_intent, default_missing

    def _extractProfileUpdates(self, intentInfo: IntentInfo) -> Dict[str, Any]:
        """从意图信息中提取用户画像更新
        
        Args:
            intentInfo: 意图信息对象
            
        Returns:
            Dict[str, Any]: 用户画像更新信息
        """
        profileUpdates = {
            "lastIntentInfo": {
                "goal": intentInfo.get('goal'),
                "audience": intentInfo.get('audience'),
                "activityType": intentInfo.get('activityType'),
                "budget": intentInfo.get('activityBudget'),
                "timestamp": self._getCurrentTimestamp()
            },
            "preferences": {
                "commonGoals": [intentInfo.get('goal')],
                "commonAudiences": [intentInfo.get('audience')] if intentInfo.get('audience') else [],
                "budgetRange": intentInfo.get('activityBudget') if intentInfo.get('activityBudget') else None
            }
        }
        return profileUpdates

    def _extractJsonFromResponse(self, response_text: str) -> str:
        """
        从响应中提取JSON内容，跳过reasoning标签

        Args:
            response_text: LLM的完整响应

        Returns:
            str: 提取的JSON字符串

        Raises:
            ValueError: 无法找到有效的JSON内容
        """
        if not response_text:
            raise ValueError("响应文本为空")

        # 移除reasoning标签内容
        import re

        # 移除 <reasoning>...</reasoning> 部分
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        cleaned_text = re.sub(reasoning_pattern, '', response_text, flags=re.DOTALL)

        # 清理剩余文本
        cleaned_text = cleaned_text.strip()

        # 移除可能的代码块标记
        if cleaned_text.startswith("```json"):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.startswith("```"):
            cleaned_text = cleaned_text[3:]
        if cleaned_text.endswith("```"):
            cleaned_text = cleaned_text[:-3]

        cleaned_text = cleaned_text.strip()

        # 尝试多种方法提取JSON

        # 方法1: 查找完整的JSON对象
        start_index = cleaned_text.find('{')
        if start_index != -1:
            # 找到匹配的结束括号
            bracket_count = 0
            end_index = -1

            for i, char in enumerate(cleaned_text[start_index:], start_index):
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        end_index = i
                        break

            if end_index != -1:
                json_content = cleaned_text[start_index:end_index + 1]

                # 验证JSON格式
                try:
                    json.loads(json_content)
                    return json_content
                except json.JSONDecodeError:
                    pass  # 继续尝试其他方法

        # 方法2: 如果找不到完整的JSON，尝试查找最后一个}
        if start_index != -1:
            last_brace = cleaned_text.rfind('}')
            if last_brace > start_index:
                json_content = cleaned_text[start_index:last_brace + 1]
                try:
                    json.loads(json_content)
                    return json_content
                except json.JSONDecodeError:
                    pass

        # 方法3: 尝试修复常见的JSON格式问题
        if start_index != -1:
            # 取从第一个{到文本结尾，然后尝试添加缺失的}
            partial_json = cleaned_text[start_index:]

            # 计算需要的}数量
            open_braces = partial_json.count('{')
            close_braces = partial_json.count('}')
            missing_braces = open_braces - close_braces

            if missing_braces > 0:
                # 添加缺失的}
                fixed_json = partial_json + '}' * missing_braces
                try:
                    json.loads(fixed_json)
                    return fixed_json
                except json.JSONDecodeError:
                    pass

        # 如果所有方法都失败，抛出详细错误
        raise ValueError(f"无法提取有效的JSON内容。清理后的文本: {cleaned_text[:200]}...")

