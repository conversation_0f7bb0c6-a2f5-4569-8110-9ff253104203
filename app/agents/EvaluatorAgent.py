"""
方案评估Agent，负责评估和优化营销方案
"""

import json
from typing import Dict, Any
from app.agents.base import LLMAgent, AgentResult
from app.utils.models import PlanFeedback
from app.llm.LlmFactory import getDefaultLLM
from app.utils.prompts import planEvaluationPrompt
from app.agents.base import agentLogDecorator


class EvaluatorAgent(LLMAgent):
    """
    方案评估Agent
    
    负责评估营销方案质量，提供优化建议
    """
    
    def __init__(self):
        """初始化方案评估Agent"""
        super().__init__("EvaluatorAgent", getDefaultLLM())
    
    @agentLogDecorator
    def execute(self, state: Dict[str, Any]) -> AgentResult:
        """
        执行方案评估逻辑
        
        Args:
            state: 当前状态
            
        Returns:
            AgentResult: 执行结果
        """
        try:
            # 获取方案草稿
            planDraft = state.get("planDraft")
            
            if not planDraft:
                return AgentResult(
                    success=False,
                    data={},
                    error="方案草稿不能为空",
                    metadata={"agent": self.name}
                )
            
            # 使用LLM评估方案
            planFeedback = self._evaluatePlanWithLLM(planDraft)
            
            if not planFeedback:
                return AgentResult(
                    success=False,
                    data={},
                    error="无法评估营销方案",
                    metadata={"agent": self.name}
                )
            
            # 更新状态
            state.update({
                "planFeedback": planFeedback,
                "currentStep": "plan_evaluation_completed"
            })
            
            return AgentResult(
                success=True,
                data={"planFeedback": planFeedback},
                metadata={"agent": self.name}
            )
            
        except Exception as e:
            self.logger.error(f"方案评估Agent执行失败: {str(e)}")
            return AgentResult(
                success=False,
                data={},
                error=str(e),
                metadata={"agent": self.name}
            )
    
    def _evaluatePlanWithLLM(self, planDraft) -> PlanFeedback:
        """
        使用LLM评估营销方案
        
        Args:
            planDraft: 方案草稿
            
        Returns:
            PlanFeedback: 方案反馈
        """
        try:
            # 构造prompt
            prompt = planEvaluationPrompt.format(plan_draft=planDraft.content)
            
            # 调用LLM
            if not self.llm:
                raise ValueError("LLM实例未设置")
            
            response = self.llm.invoke(prompt)
            
            # 解析响应
            if hasattr(response, 'content'):
                responseText = response.content
            else:
                responseText = str(response)
            
            # 尝试解析JSON
            try:
                feedbackData = json.loads(responseText)
            except json.JSONDecodeError:
                # 如果不是JSON，使用默认值
                feedbackData = {
                    "satisfied": True,
                    "comments": responseText
                }
            
            # 创建PlanFeedback对象
            planFeedback = PlanFeedback(
                satisfied=feedbackData.get("satisfied", True),
                comments=feedbackData.get("comments", "")
            )
            
            return planFeedback
            
        except Exception as e:
            self.logger.error(f"LLM方案评估失败: {str(e)}")
            # 返回默认的PlanFeedback，而不是None
            return PlanFeedback(
                satisfied=False,
                comments=f"评估失败: {str(e)}"
            ) 