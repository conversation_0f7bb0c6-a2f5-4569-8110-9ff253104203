"""
增强版营销路由Agent - 智能路由决策引擎
设计思路：
1. 混合架构：结合ReAct循环和强制核心步骤的混合路由方式
2. LLM增强：使用大模型进行智能分析和决策
3. 提示词集中：所有提示词统一管理在Agent内部
4. 驼峰命名：遵循统一的命名规范
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from langchain_core.tools import tool
from langchain_core.messages import HumanMessage
from langgraph.prebuilt import create_react_agent

from app.agents.base import BaseAgent, agentLogDecorator
from app.utils.models import MarketingState
from app.llm.LlmFactory import getDefaultLLM

# Import tools from the agents tools directory
from app.agents.tools.RouterAgentTools import (
    stateAssessmentTool,
    intentAnalysisTypeTool,
    routerTool
)

# Import prompts from the prompts directory
from app.agents.prompts.router_prompts import RouterPrompts


class RouterAgent(BaseAgent):
    """
    营销路由Agent - 智能路由决策引擎

    设计思路：
    - 混合架构：结合强制核心步骤和ReAct循环的智能路由方式
    - LLM增强：使用大模型进行智能分析和决策
    - 提示词集中：所有提示词统一管理在Agent内部
    - 驼峰命名：遵循统一的命名规范
    """

    def __init__(self, maxRetries: int = 3, llmConfidenceThreshold: float = 0.6, enableLlmRouting: bool = True):
        """
        初始化路由Agent

        Args:
            maxRetries: 最大重试次数
            llmConfidenceThreshold: LLM决策置信度阈值
            enableLlmRouting: 是否启用LLM路由（兼容工作流参数）
        """
        super().__init__("RouterAgent")

        self.maxRetries = maxRetries
        self.llmConfidenceThreshold = llmConfidenceThreshold
        self.enableLlmRouting = enableLlmRouting

        # 初始化LLM
        self.llm = getDefaultLLM()

        # 可用Agent节点定义（用于验证）
        self.availableAgents = ["INTENTION_AGENT", "TAG_CUSTOMER_AGENT", "HISTORY_RAG_AGENT", "PLAN_CREATOR_AGENT", "END"]

        # 初始化工具
        self.tools = self.initializeTools()

        # 创建标准ReAct Agent
        self.reactAgent = create_react_agent(self.llm, self.tools)

        # 获取LLM模型信息用于日志
        import os
        model_name = os.getenv("LLM_MODEL", "未知模型")

        self.logger.info(f"🧭 路由Agent初始化完成 - 智能路由: {'启用' if enableLlmRouting else '禁用'}, 模型: {model_name}, 工具: {len(self.tools)}个, 置信度: {llmConfidenceThreshold}")

    def initializeTools(self) -> List:
        """
        初始化智能路由工具列表

        专注于路由决策所需的核心工具：
        1. 状态评估 - 了解信息完整度
        2. 意图类型分析 - LLM增强的意图类型识别
        3. 路由决策 - 基于分析结果选择最佳路径
        """
        return [
            stateAssessmentTool,
            intentAnalysisTypeTool,
            routerTool
        ]
    @agentLogDecorator
    def execute(self, state: MarketingState) -> MarketingState:
        """
        路由Agent核心执行逻辑 - LLM增强版

        设计思路：
        1. 使用LLM智能分析和调用工具
        2. LLM推理 + 规则验证的混合决策
        3. 多层兜底机制确保系统稳定
        4. 结构化输出和完整追踪
        """
        try:
            userInput = state.get("userInput", "")
            self.logger.info(f"路由Agent开始处理用户输入: {userInput[:50]}...")

            # 获取stream writer（在整个方法中使用）
            writer = None
            try:
                from langgraph.config import get_stream_writer
                writer = get_stream_writer()
            except Exception as e:
                self.logger.debug(f"获取stream writer失败: {e}")

            # 发送用户友好的进度消息
            try:
                if writer:
                    writer({
                        "type": "nodeProgress",
                        "nodeId": "ROUTER_AGENT",
                        "stage": "analysis_start",
                        "content": "正在分析您的营销需求，请稍候...",
                        "progress": 20
                    })
            except Exception as e:
                self.logger.debug(f"发送进度数据失败: {e}")

            # LLM增强执行路径
            result = self.llmEnhancedExecution(state)

            if result:
                self.logger.info(f"LLM增强执行成功: {result.get('nextStep', 'unknown')}")

                # 发送用户友好的完成消息
                try:
                    if writer:
                        next_step_name = self._get_friendly_step_name(result.get('nextNode', 'unknown'))
                        writer({
                            "type": "nodeProgress",
                            "nodeId": "ROUTER_AGENT",
                            "stage": "decision_complete",
                            "content": f"需求分析完成，接下来将{next_step_name}",
                            "progress": 100
                        })
                except Exception as e:
                    self.logger.debug(f"发送结果数据失败: {e}")

                return result
            else:
                self.logger.warning("LLM增强执行失败，使用兜底逻辑")
                return self.fallbackRouting(state)

        except Exception as e:
            self.logger.error(f"路由Agent执行失败: {str(e)}")
            # 降级到原有的兜底逻辑
            return self.fallbackRouting(state)

    def _analyzeMissingInfo(self, state: MarketingState) -> List[str]:
        """分析当前状态缺失的信息"""
        missing = []
        if not state.get("intentInfo"):
            missing.append("意图信息")
        if not state.get("customerTags"):
            missing.append("客群标签")
        if not state.get("ragContext"):
            missing.append("历史数据")
        if not state.get("planDraft"):
            missing.append("方案草稿")
        return missing

    def _get_friendly_step_name(self, node_name: str) -> str:
        """将技术节点名转换为用户友好名称"""
        name_mapping = {
            "INTENTION_AGENT": "深入了解您的具体需求",
            "TAG_CUSTOMER_AGENT": "分析目标客群特征",
            "HISTORY_RAG_AGENT": "查找相关成功案例",
            "PLAN_CREATOR_AGENT": "制定详细营销方案",
            "COMPLETE_CUSTOMER_NODE": "完善客群信息",
            "COMPLETE_PLAN_NODE": "优化方案细节",
            "COMPLETE_INTENT_NODE": "完善营销目标"
        }
        return name_mapping.get(node_name, "继续处理您的需求")

    def llmEnhancedExecution(self, state: MarketingState) -> Optional[MarketingState]:
        """
        上下文感知的智能路由执行

        设计思路：
        1. 使用ReAct Agent智能分析当前状态
        2. 根据用户类型和信息完整度选择工具
        3. 避免重复分析已有信息
        4. 生成个性化的路由决策
        """
        try:
            userInput = state.get("userInput", "")
            self.logger.info(f"开始智能路由分析，用户输入: {userInput[:50]}...")

            # 构建上下文感知的提示词
            contextPrompt = self.buildContextAwarePrompt(state)

            # 使用ReAct Agent智能执行路由分析
            routingResult = self.executeSmartRoutingAnalysis(state, contextPrompt)

            if not routingResult:
                self.logger.warning("智能路由分析失败，使用兜底逻辑")
                return self.fallbackRouting(state)

            # 生成最终路由决策
            nextNode = routingResult.get('nextNode', 'INTENTION_AGENT')
            confidence = routingResult.get('confidence', 0.5)
            reasoning = routingResult.get('reasoning', '默认路由逻辑')

            self.logger.info(f"智能路由决策完成: {nextNode} (置信度: {confidence})")
            self.logger.debug(f"决策理由: {reasoning}")

            # 更新状态并返回
            state["nextNode"] = nextNode
            state["routingConfidence"] = confidence
            state["routingReasoning"] = reasoning

            return state

        except Exception as e:
            self.logger.error(f"智能路由执行失败: {str(e)}")
            return self.fallbackRouting(state)

    def buildContextAwarePrompt(self, state: MarketingState) -> str:
        """
        构建上下文感知的路由分析提示词

        根据当前状态信息，生成智能的分析提示词，
        指导LLM选择合适的工具和路由策略
        """
        userInput = state.get("userInput", "")

        # 使用提示词文件中的方法生成状态摘要
        existingInfo = RouterPrompts.summarize_existing_info(state)

        # 构建基础提示词
        base_prompt = RouterPrompts.build_context_aware_prompt(userInput, existingInfo)

        # 添加状态数据传递指导
        state_data_json = json.dumps({
            "intentInfo": state.get("intentInfo"),
            "customerTags": state.get("customerTags"),
            "ragContext": state.get("ragContext"),
            "historicalCases": state.get("historicalCases"),
            "planDraft": state.get("planDraft"),
            "currentStep": state.get("currentStep")
        }, ensure_ascii=False)

        enhanced_prompt = f"""{base_prompt}

## 🔥 重要：必须按顺序调用所有工具
为了确保路由决策的准确性，你必须按以下顺序调用所有3个工具：

1. 【必须】stateAssessmentTool(currentStateInfo="{existingInfo}", userInput="{userInput}")
2. 【必须】intentAnalysisTypeTool(userInput="{userInput}")
3. 【必须】routerTool(stateAssessment="第1步的结果", intentAnalysis="第2步的结果")

## 关键修复说明
- routerTool现在会自动检查真实的Agent执行结果
- 这样可以防止在没有真正的IntentionAgent和TagCustomerAgent结果时跳过必要步骤
- 请确保每个工具都被调用，不要跳过任何步骤

请严格按照上述格式调用工具。"""

        return enhanced_prompt



    def executeSmartRoutingAnalysis(self, state: MarketingState, prompt: str) -> Dict[str, Any]:
        """
        使用标准ReAct Agent执行智能路由分析

        让ReAct Agent自动选择和调用工具，
        专注于路由决策所需的信息收集
        """
        try:
            self.logger.debug("开始ReAct Agent智能分析")

            # 构建消息
            messages = [HumanMessage(content=prompt)]

            # 调用ReAct Agent，通过config传递state数据
            config = {
                "configurable": {
                    "marketing_state": state  # 通过config传递完整state
                }
            }

            result = self.reactAgent.invoke(
                {"messages": messages},
                config=config
            )

            # 提取最终的路由决策
            routingDecision = self.extractRoutingDecisionFromResult(result)

            if not routingDecision:
                self.logger.warning("ReAct Agent未产生有效路由决策，生成默认决策")
                routingDecision = self.generateDefaultRoutingDecision(state)

            self.logger.debug(f"ReAct Agent分析完成，决策: {routingDecision.get('nextNode', 'unknown')}")
            return routingDecision

        except Exception as e:
            self.logger.error(f"ReAct Agent分析失败: {str(e)}")
            return self.generateDefaultRoutingDecision(state)

    def extractRoutingDecisionFromResult(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        从ReAct Agent的执行结果中提取路由决策

        ReAct Agent会在messages中包含工具调用的结果，
        我们需要找到RouterTool的输出
        """
        try:
            messages = result.get("messages", [])

            # 从后往前查找，找到最后一个工具调用结果
            for message in reversed(messages):
                # 检查是否是工具调用结果
                if hasattr(message, 'content') and isinstance(message.content, str):
                    content = message.content

                    # 尝试解析JSON格式的路由决策
                    if 'nextNode' in content or 'routingDecision' in content:
                        try:
                            # 清理并解析JSON
                            cleanContent = self.cleanJsonResponse(content)
                            decision = json.loads(cleanContent)

                            # 验证是否包含必要字段
                            if 'nextNode' in decision:
                                return decision

                        except json.JSONDecodeError:
                            continue

                # 检查是否是工具消息
                if hasattr(message, 'tool_calls') and message.tool_calls:
                    for tool_call in message.tool_calls:
                        if 'routing' in tool_call.get('name', '').lower():
                            # 这是路由决策工具的调用，查找对应的结果
                            continue

            # 如果没有找到明确的路由决策，尝试从最后一条消息中提取
            if messages:
                last_message = messages[-1]
                if hasattr(last_message, 'content'):
                    content = last_message.content
                    if isinstance(content, str) and len(content) > 10:
                        # 尝试从最后的消息中提取决策信息
                        return self.parseDecisionFromText(content)

            return None

        except Exception as e:
            self.logger.error(f"提取路由决策失败: {str(e)}")
            return None

    def parseDecisionFromText(self, text: str) -> Dict[str, Any]:
        """
        从文本中解析路由决策信息

        当无法直接获取JSON格式结果时的兜底解析
        """
        try:
            # 查找常见的Agent名称
            agentNames = ["INTENTION_AGENT", "TAG_CUSTOMER_AGENT", "HISTORY_RAG_AGENT", "PLAN_CREATOR_AGENT"]

            for agentName in agentNames:
                if agentName in text.upper():
                    return {
                        "nextNode": agentName,
                        "confidence": 0.6,
                        "reasoning": f"从文本中识别到{agentName}",
                        "strategy": "text_parsing"
                    }

            # 如果没有找到明确的Agent名称，根据关键词推断
            if "意图" in text or "intent" in text.lower():
                return {
                    "nextNode": "INTENTION_AGENT",
                    "confidence": 0.5,
                    "reasoning": "文本提到意图相关内容",
                    "strategy": "keyword_inference"
                }

            return None

        except Exception as e:
            self.logger.error(f"文本解析失败: {str(e)}")
            return None



    def summarizeCurrentState(self, state: MarketingState) -> str:
        """总结当前状态"""
        summaryParts = []

        if state.get("intentInfo"):
            summaryParts.append("已有意图信息")
        else:
            summaryParts.append("缺少意图信息")

        if state.get("customerTags"):
            summaryParts.append("已有客户标签")
        else:
            summaryParts.append("缺少客户标签")

        if state.get("historicalCases"):
            summaryParts.append("已查询历史数据")
        else:
            summaryParts.append("未查询历史数据")

        return "，".join(summaryParts)

    # 旧的fallbackRouting方法已删除，使用下面统一的新版本





    def cleanJsonResponse(self, responseText: str) -> str:
        """
        清理LLM响应文本，提取JSON部分
        """
        # 移除常见的前缀和后缀
        responseText = responseText.strip()

        # 查找JSON开始和结束位置
        start_idx = responseText.find('{')
        end_idx = responseText.rfind('}')

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return responseText[start_idx:end_idx + 1]

        return responseText







    def generateDefaultRoutingDecision(self, state: MarketingState) -> Dict[str, Any]:
        """
        生成默认路由决策

        当智能分析失败时的兜底逻辑
        """
        return {
            "nextNode": "INTENTION_AGENT",
            "confidence": 0.4,
            "reasoning": "使用默认路由逻辑，从意图分析开始",
            "userStyle": "unknown",
            "strategy": "default"
        }

    def fallbackRouting(self, state: MarketingState) -> MarketingState:
        """
        兜底路由逻辑

        当所有智能分析都失败时使用的简单路由
        """
        try:
            # 简单的规则路由
            if not state.get("intentInfo"):
                nextNode = "INTENTION_AGENT"
                reasoning = "缺少意图信息，路由到意图分析"
            elif not state.get("customerTags"):
                nextNode = "TAG_CUSTOMER_AGENT"
                reasoning = "缺少客户标签，路由到客户分析"
            elif not state.get("ragContext"):
                nextNode = "HISTORY_RAG_AGENT"
                reasoning = "缺少历史数据，路由到数据检索"
            else:
                nextNode = "PLAN_CREATOR_AGENT"
                reasoning = "信息基本完整，路由到方案生成"

            state["nextNode"] = nextNode
            state["routingConfidence"] = 0.3
            state["routingReasoning"] = reasoning
            state["currentStep"] = "enhancedRouterCompleted"

            self.logger.info(f"兜底路由决策: {nextNode}")
            return state

        except Exception as e:
            self.logger.error(f"兜底路由失败: {str(e)}")
            # 最后的兜底
            state["nextNode"] = "INTENTION_AGENT"
            state["routingConfidence"] = 0.1
            state["routingReasoning"] = "所有路由逻辑失败，使用最基础路由"
            return state
