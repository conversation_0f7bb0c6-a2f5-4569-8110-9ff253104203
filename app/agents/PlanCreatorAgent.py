"""
方案生成Agent，负责生成营销方案
简化版本，直接流式输出
"""

import json
from app.agents.base import LLMAgent, agentLogDecorator
from app.utils.models import MarketingState
from app.llm.LlmFactory import getDefaultLLM


class PlanCreatorAgent(LLMAgent):
    """简化的方案生成Agent"""

    def __init__(self):
        """初始化方案生成Agent"""
        super().__init__("PlanCreatorAgent", getDefaultLLM())

    @agentLogDecorator
    def execute(self, state: MarketingState) -> MarketingState:
        """直接流式生成方案"""
        try:
            intentInfo = state.get("intentInfo")
            ragContext = state.get("ragContext")

            if not intentInfo:
                return state

            # 构造prompt并流式生成
            prompt = f"""根据以下信息生成营销方案：

意图信息：{json.dumps(intentInfo, ensure_ascii=False)}
历史案例：{json.dumps(ragContext, ensure_ascii=False)}

请生成完整的营销方案，包含活动概述、目标客群、策略等。"""

            response = self.llm.invoke(prompt)
            full_response = getattr(response, 'content', str(response))
            print(full_response)  # 直接输出

            # 更新状态
            planDraft = {"content": full_response, "outline": []}
            state.update({
                "planDraft": planDraft,
                "currentStep": "plan_generation_completed"
            })

            return state

        except Exception as e:
            self.logger.error(f"方案生成失败: {str(e)}")
            return state

