"""
历史数据检索Agent
设计思路：
1. 当用户明确要求生成方案时，主动查询历史营销案例数据库
2. 提取最佳实践和成功模式，为方案生成提供参考
3. 减少对用户的依赖，提高自动化程度
4. 支持多维度的数据检索和分析
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from app.agents.base import BaseAgent, AgentResult, agentLogDecorator
from app.utils.models import MarketingState
from app.llm.LlmFactory import getDefaultLLM


class HistoryRAGAgent(BaseAgent):
    """
    历史营销数据检索Agent
    
    设计思路：
    - 基于用户意图信息智能查询历史营销案例
    - 提取成功案例的关键要素和最佳实践
    - 为后续的方案生成提供数据支撑
    - 支持多种查询策略和数据源
    """
    
    def __init__(self):
        """初始化历史数据检索Agent"""
        super().__init__("HistoryRAGAgent")
        
        # 初始化数据源配置（第一阶段使用模拟数据）
        self.dataSourceConfig = self._initializeDataSources()
        
        # 查询策略配置
        self.queryStrategies = self._initializeQueryStrategies()

        self.logger.info(f"📚 历史数据检索Agent初始化完成 - 数据源: {len(self.dataSourceConfig)}个, 查询策略: {len(self.queryStrategies)}种")

    def _initializeDataSources(self) -> Dict[str, Any]:
        """
        初始化数据源配置
        设计思路：为不同类型的数据源提供统一的访问接口
        """
        return {
            "marketingCaseDb": {
                "type": "database",
                "description": "历史营销案例数据库",
                "available": True,
                "priority": "high"
            },
            "customerBehaviorDb": {
                "type": "database", 
                "description": "客户行为数据库",
                "available": True,
                "priority": "high"
            },
            "industryBenchmarkDb": {
                "type": "database",
                "description": "行业基准数据库", 
                "available": True,
                "priority": "medium"
            },
            "knowledgeBase": {
                "type": "knowledge_base",
                "description": "营销知识库",
                "available": True,
                "priority": "medium"
            }
        }

    def _initializeQueryStrategies(self) -> Dict[str, Any]:
        """
        初始化查询策略
        设计思路：根据不同的营销场景采用不同的查询策略
        """
        return {
            "productBased": {
                "description": "基于产品类型的查询策略",
                "queryFields": ["productType", "activityType"],
                "weight": 0.4
            },
            "audienceBased": {
                "description": "基于目标客群的查询策略", 
                "queryFields": ["audience", "customerSegment"],
                "weight": 0.3
            },
            "channelBased": {
                "description": "基于营销渠道的查询策略",
                "queryFields": ["channels", "touchpoints"],
                "weight": 0.2
            },
            "goalBased": {
                "description": "基于营销目标的查询策略",
                "queryFields": ["goal", "objectives"],
                "weight": 0.1
            }
        }

    @agentLogDecorator
    def execute(self, state: MarketingState) -> MarketingState:
        """
        历史数据检索核心执行逻辑
        
        设计思路：
        1. 分析用户意图信息，确定查询重点
        2. 执行多维度的数据检索
        3. 提取最佳实践和成功模式
        4. 生成客户标签建议
        """
        try:
            intentInfo = state.get("intentInfo", {})
            self.logger.info(f"历史数据检索Agent开始执行，意图信息: {intentInfo}")

            # 步骤1: 规划查询策略
            queryPlan = self._planDataQuery(intentInfo)
            self.logger.debug(f"查询计划生成完成: {len(queryPlan['queries'])}个查询任务")

            # 步骤2: 执行历史案例检索
            historicalCases = self._searchHistoricalCases(queryPlan)
            self.logger.debug(f"历史案例检索完成: 找到{len(historicalCases)}个相关案例")

            # 步骤3: 提取最佳实践
            bestPractices = self._extractBestPractices(historicalCases)
            self.logger.debug(f"最佳实践提取完成: {len(bestPractices)}条实践指南")

            # 步骤4: 生成客户标签建议
            suggestedCustomerTags = self._generateCustomerTagSuggestions(intentInfo, historicalCases)
            self.logger.debug(f"客户标签建议生成完成: {len(suggestedCustomerTags)}个标签")

            # 步骤5: 分析成功模式
            successPatterns = self._analyzeSuccessPatterns(historicalCases)
            self.logger.debug(f"成功模式分析完成: {len(successPatterns)}个模式")

            # 步骤6: 更新状态
            state.update({
                "historicalCases": historicalCases,
                "bestPractices": bestPractices,
                "suggestedCustomerTags": suggestedCustomerTags,
                "successPatterns": successPatterns,
                "dataSourcesQueried": True,
                "queryPlan": queryPlan,
                "currentStep": "historyRAGCompleted"
            })

            self.logger.info(f"历史数据检索完成，共获取{len(historicalCases)}个案例")
            return state

        except Exception as e:
            self.logger.error(f"历史数据检索失败: {str(e)}")
            # 设置默认值，确保流程继续
            state.update({
                "historicalCases": [],
                "bestPractices": [],
                "suggestedCustomerTags": [],
                "dataSourcesQueried": False,
                "historyRAGError": str(e),
                "currentStep": "historyRAGError"
            })
            return state

    def _planDataQuery(self, intentInfo: Dict[str, Any]) -> Dict[str, Any]:
        """
        规划数据查询策略
        设计思路：基于意图信息确定查询重点和优先级
        """
        try:
            queries = []
            
            # 基于产品类型的查询
            if intentInfo.get("productType"):
                queries.append({
                    "type": "productBased",
                    "query": f"产品类型:{intentInfo['productType']}",
                    "dataSource": "marketingCaseDb",
                    "priority": "high",
                    "expectedResults": 5
                })
            
            # 基于目标客群的查询
            if intentInfo.get("audience"):
                queries.append({
                    "type": "audienceBased", 
                    "query": f"目标客群:{intentInfo['audience']}",
                    "dataSource": "customerBehaviorDb",
                    "priority": "high",
                    "expectedResults": 3
                })
            
            # 基于营销目标的查询
            if intentInfo.get("goal"):
                queries.append({
                    "type": "goalBased",
                    "query": f"营销目标:{intentInfo['goal']}",
                    "dataSource": "marketingCaseDb", 
                    "priority": "medium",
                    "expectedResults": 3
                })
            
            # 基于活动类型的查询
            if intentInfo.get("activityType"):
                queries.append({
                    "type": "channelBased",
                    "query": f"活动类型:{intentInfo['activityType']}",
                    "dataSource": "knowledgeBase",
                    "priority": "medium",
                    "expectedResults": 2
                })
            
            return {
                "queries": queries,
                "totalQueries": len(queries),
                "estimatedDuration": len(queries) * 5,  # 每个查询预估5秒
                "queryStrategy": "comprehensive"
            }
            
        except Exception as e:
            self.logger.error(f"查询规划失败: {str(e)}")
            return {
                "queries": [],
                "totalQueries": 0,
                "estimatedDuration": 0,
                "queryStrategy": "fallback"
            }

    def _searchHistoricalCases(self, queryPlan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        搜索历史营销案例
        设计思路：执行多维度查询，获取相关的历史案例
        """
        try:
            allCases = []
            
            for query in queryPlan.get("queries", []):
                # 第一阶段：使用模拟数据，后续可连接实际数据库
                cases = self._mockHistoricalCaseQuery(query)
                allCases.extend(cases)
            
            # 去重和排序
            uniqueCases = self._deduplicateAndRankCases(allCases)
            
            # 限制返回数量
            maxCases = 10
            return uniqueCases[:maxCases]
            
        except Exception as e:
            self.logger.error(f"历史案例搜索失败: {str(e)}")
            return []

    def _mockHistoricalCaseQuery(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        模拟历史案例查询（第一阶段使用，后续替换为实际数据库查询）
        """
        # 模拟数据，实际应该连接数据库
        mockCases = [
            {
                "caseId": "case_001",
                "title": "信用卡新客获取营销活动",
                "productType": "信用卡",
                "audience": "年轻白领",
                "goal": "新客获取",
                "channels": ["线上广告", "社交媒体"],
                "roi": 3.2,
                "conversionRate": 0.15,
                "successFactors": ["精准定位", "优惠力度", "申请便利性"],
                "timeline": "3个月",
                "budget": "100万",
                "relevanceScore": 0.9
            },
            {
                "caseId": "case_002", 
                "title": "存款产品交叉销售活动",
                "productType": "存款",
                "audience": "中高收入客户",
                "goal": "交叉销售",
                "channels": ["客户经理", "手机银行"],
                "roi": 2.8,
                "conversionRate": 0.22,
                "successFactors": ["客户关系", "产品匹配", "时机把握"],
                "timeline": "2个月",
                "budget": "50万",
                "relevanceScore": 0.7
            }
        ]
        
        # 根据查询类型过滤相关案例
        queryType = query.get("type", "")
        queryText = query.get("query", "").lower()
        
        relevantCases = []
        for case in mockCases:
            if self._calculateCaseRelevance(case, queryText, queryType) > 0.5:
                relevantCases.append(case)
        
        return relevantCases

    def _calculateCaseRelevance(self, case: Dict[str, Any], queryText: str, queryType: str) -> float:
        """计算案例相关性评分"""
        relevanceScore = 0.0
        
        # 基于查询类型计算相关性
        if queryType == "productBased" and queryText in case.get("productType", "").lower():
            relevanceScore += 0.4
        elif queryType == "audienceBased" and queryText in case.get("audience", "").lower():
            relevanceScore += 0.3
        elif queryType == "goalBased" and queryText in case.get("goal", "").lower():
            relevanceScore += 0.2
        
        # 基于成功指标加权
        if case.get("roi", 0) > 2.0:
            relevanceScore += 0.1
        if case.get("conversionRate", 0) > 0.1:
            relevanceScore += 0.1
        
        return min(1.0, relevanceScore)

    def _deduplicateAndRankCases(self, cases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重和排序案例"""
        # 简单去重（基于caseId）
        uniqueCases = {}
        for case in cases:
            caseId = case.get("caseId")
            if caseId not in uniqueCases:
                uniqueCases[caseId] = case
        
        # 按相关性评分排序
        sortedCases = sorted(
            uniqueCases.values(),
            key=lambda x: x.get("relevanceScore", 0),
            reverse=True
        )
        
        return sortedCases

    def _extractBestPractices(self, historicalCases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从历史案例中提取最佳实践
        设计思路：分析成功案例的共同特征，提取可复用的实践指南
        """
        try:
            bestPractices = []

            if not historicalCases:
                return bestPractices

            # 分析成功因素
            allSuccessFactors = []
            for case in historicalCases:
                if case.get("roi", 0) > 2.0:  # 只分析高ROI案例
                    allSuccessFactors.extend(case.get("successFactors", []))

            # 统计成功因素频次
            factorCounts = {}
            for factor in allSuccessFactors:
                factorCounts[factor] = factorCounts.get(factor, 0) + 1

            # 生成最佳实践
            for factor, count in sorted(factorCounts.items(), key=lambda x: x[1], reverse=True):
                if count >= 2:  # 至少在2个案例中出现
                    bestPractices.append({
                        "practice": factor,
                        "frequency": count,
                        "confidence": min(1.0, count / len(historicalCases)),
                        "category": self._categorizePractice(factor),
                        "description": f"在{count}个成功案例中都采用了{factor}策略"
                    })

            # 添加通用最佳实践
            if historicalCases:
                avgROI = sum(case.get("roi", 0) for case in historicalCases) / len(historicalCases)
                avgConversion = sum(case.get("conversionRate", 0) for case in historicalCases) / len(historicalCases)

                bestPractices.append({
                    "practice": "数据驱动决策",
                    "frequency": len(historicalCases),
                    "confidence": 0.9,
                    "category": "strategy",
                    "description": f"基于历史数据，平均ROI为{avgROI:.1f}，转化率为{avgConversion:.2%}"
                })

            return bestPractices[:10]  # 限制返回数量

        except Exception as e:
            self.logger.error(f"最佳实践提取失败: {str(e)}")
            return []

    def _categorizePractice(self, practice: str) -> str:
        """对最佳实践进行分类"""
        categoryMap = {
            "精准定位": "targeting",
            "优惠力度": "incentive",
            "申请便利性": "experience",
            "客户关系": "relationship",
            "产品匹配": "product",
            "时机把握": "timing"
        }
        return categoryMap.get(practice, "general")

    def _generateCustomerTagSuggestions(self, intentInfo: Dict[str, Any],
                                      historicalCases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        生成客户标签建议
        设计思路：基于历史案例和意图信息，推荐合适的客户标签
        """
        try:
            suggestions = []

            # 基于产品类型推荐标签
            productType = intentInfo.get("productType", "")
            if productType:
                productTagMap = {
                    "信用卡": ["年轻白领", "消费活跃", "信用良好"],
                    "存款": ["中高收入", "风险偏好低", "资金充裕"],
                    "理财": ["投资意识强", "风险承受能力中等", "追求收益"]
                }

                if productType in productTagMap:
                    for tag in productTagMap[productType]:
                        suggestions.append({
                            "tag": tag,
                            "category": "demographic",
                            "confidence": 0.8,
                            "source": "product_mapping",
                            "description": f"基于{productType}产品特性推荐"
                        })

            # 基于历史案例推荐标签
            for case in historicalCases:
                if case.get("roi", 0) > 2.5:  # 高ROI案例
                    audience = case.get("audience", "")
                    if audience:
                        suggestions.append({
                            "tag": audience,
                            "category": "behavioral",
                            "confidence": 0.7,
                            "source": "historical_success",
                            "description": f"在ROI {case.get('roi', 0):.1f}的成功案例中使用"
                        })

            # 去重
            uniqueSuggestions = {}
            for suggestion in suggestions:
                tag = suggestion["tag"]
                if tag not in uniqueSuggestions or suggestion["confidence"] > uniqueSuggestions[tag]["confidence"]:
                    uniqueSuggestions[tag] = suggestion

            return list(uniqueSuggestions.values())[:8]  # 限制返回数量

        except Exception as e:
            self.logger.error(f"客户标签建议生成失败: {str(e)}")
            return []

    def _analyzeSuccessPatterns(self, historicalCases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分析成功模式
        设计思路：识别高成功率案例的共同模式和特征
        """
        try:
            patterns = []

            if not historicalCases:
                return patterns

            # 分析高ROI案例的模式
            highROICases = [case for case in historicalCases if case.get("roi", 0) > 2.5]
            if highROICases:
                # 渠道模式分析
                channelCombinations = {}
                for case in highROICases:
                    channels = tuple(sorted(case.get("channels", [])))
                    channelCombinations[channels] = channelCombinations.get(channels, 0) + 1

                for channels, count in channelCombinations.items():
                    if count >= 2:
                        patterns.append({
                            "pattern": "高效渠道组合",
                            "details": list(channels),
                            "frequency": count,
                            "avgROI": sum(case.get("roi", 0) for case in highROICases
                                        if tuple(sorted(case.get("channels", []))) == channels) / count,
                            "category": "channel"
                        })

            # 分析时间模式
            timelinePatterns = {}
            for case in historicalCases:
                timeline = case.get("timeline", "")
                roi = case.get("roi", 0)
                if timeline and roi > 2.0:
                    if timeline not in timelinePatterns:
                        timelinePatterns[timeline] = []
                    timelinePatterns[timeline].append(roi)

            for timeline, rois in timelinePatterns.items():
                if len(rois) >= 2:
                    patterns.append({
                        "pattern": "最优执行周期",
                        "details": timeline,
                        "frequency": len(rois),
                        "avgROI": sum(rois) / len(rois),
                        "category": "timing"
                    })

            return patterns[:5]  # 限制返回数量

        except Exception as e:
            self.logger.error(f"成功模式分析失败: {str(e)}")
            return []
