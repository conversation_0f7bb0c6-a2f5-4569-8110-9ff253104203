"""
方案生成Agent提示词模板
集中管理方案生成相关的提示词
"""

from typing import Dict


class PlanCreatorPrompts:
    """方案生成Agent提示词模板类"""
    
    @staticmethod
    def get_plan_generation_prompt() -> str:
        """
        获取方案生成提示词模板
        用于根据意图信息和历史案例生成营销方案
        """
        return """
你是一名资深营销策划师，请根据以下营销活动意图和历史案例经验，生成一份专业的营销活动方案。

## 意图信息
{intentInfo}

## 历史案例经验
{ragContext}

## 方案要求
请生成一份完整的营销活动方案，包含以下要素：

### 1. 活动概述
- 活动名称
- 活动目标
- 核心价值主张

### 2. 目标客群
- 客群画像
- 客群规模
- 客群特征分析

### 3. 活动策略
- 营销策略
- 执行策略
- 风险控制策略

### 4. 渠道规划
- 主要渠道
- 渠道组合
- 渠道预算分配

### 5. 激励设计
- 激励方式
- 激励力度
- 激励成本

### 6. 预算规划
- 总预算
- 预算分配
- 成本控制

### 7. 时间安排
- 活动周期
- 关键节点
- 执行时间表

### 8. 预期效果
- 关键指标
- 预期数据
- ROI预估

### 9. 风险评估
- 潜在风险
- 应对措施
- 备选方案

## 输出格式
请以Markdown格式输出完整方案，确保结构清晰、内容详实、可操作性强。
"""

    @staticmethod
    def get_plan_evaluation_prompt() -> str:
        """
        获取方案评估提示词模板
        用于评估生成的营销方案质量
        """
        return """
你是一名营销方案评审专家，请对以下营销活动方案进行全面评估。

## 待评估方案
{planDraft}

## 评估维度
请从以下维度进行评估：

### 1. 完整性评估 (0-10分)
- 方案要素是否齐全
- 信息是否充分详细
- 逻辑结构是否完整

### 2. 可行性评估 (0-10分)
- 目标是否现实可达
- 预算是否合理
- 执行是否可操作

### 3. 创新性评估 (0-10分)
- 策略是否有创新点
- 激励设计是否新颖
- 渠道组合是否独特

### 4. 风险控制评估 (0-10分)
- 风险识别是否全面
- 应对措施是否有效
- 备选方案是否充分

### 5. 预期效果评估 (0-10分)
- 指标设定是否合理
- 预期数据是否可信
- ROI预估是否准确

## 输出要求
请以JSON格式返回评估结果：
```json
{{
  "overallScore": 8.5,
  "dimensionScores": {{
    "completeness": 9.0,
    "feasibility": 8.0,
    "innovation": 8.5,
    "riskControl": 8.0,
    "expectedEffect": 9.0
  }},
  "strengths": [
    "目标设定明确具体",
    "客群分析深入细致",
    "渠道组合合理有效"
  ],
  "weaknesses": [
    "预算分配可以更细化",
    "风险应对措施需要加强"
  ],
  "improvements": [
    "建议增加详细的预算分解",
    "建议补充更多风险应对预案",
    "建议优化时间安排的合理性"
  ],
  "recommendation": "approve_with_modifications",
  "summary": "方案整体质量较高，建议在预算细化和风险控制方面进行优化后实施"
}}
```
"""

    @staticmethod
    def get_plan_optimization_prompt() -> str:
        """
        获取方案优化提示词模板
        用于根据评估结果优化方案
        """
        return """
你是一名营销方案优化专家，请根据评估反馈对营销方案进行优化改进。

## 原始方案
{originalPlan}

## 评估反馈
{evaluationFeedback}

## 优化要求
请根据评估反馈中的建议，对原始方案进行针对性优化：

### 1. 问题修复
- 修复评估中指出的明显问题
- 补充缺失的重要信息
- 调整不合理的设定

### 2. 结构优化
- 优化方案的逻辑结构
- 增强内容的连贯性
- 提升表达的清晰度

### 3. 内容增强
- 补充详细的执行细节
- 增加具体的数据支撑
- 完善风险控制措施

### 4. 可操作性提升
- 细化执行步骤
- 明确责任分工
- 制定监控机制

## 输出要求
请输出优化后的完整方案，并在方案开头说明主要优化点：

### 主要优化点
1. [优化点1的具体说明]
2. [优化点2的具体说明]
3. [优化点3的具体说明]

### 优化后方案
[完整的优化后方案内容]
"""

    @staticmethod
    def get_all_prompts() -> Dict[str, str]:
        """
        获取所有方案生成提示词模板的字典
        """
        return {
            "planGenerationPrompt": PlanCreatorPrompts.get_plan_generation_prompt(),
            "planEvaluationPrompt": PlanCreatorPrompts.get_plan_evaluation_prompt(),
            "planOptimizationPrompt": PlanCreatorPrompts.get_plan_optimization_prompt()
        }


# 为了向后兼容，提供直接访问的变量
PLAN_CREATOR_PROMPTS = PlanCreatorPrompts.get_all_prompts()
