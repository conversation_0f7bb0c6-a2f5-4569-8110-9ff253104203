"""
增强版路由Agent提示词模板
集中管理所有路由相关的提示词，便于维护和优化

包含的提示词和方法：
1. get_analysis_prompt() - LLM分析提示词模板
2. get_intent_analysis_prompt() - 意图分析提示词模板
3. get_state_analysis_prompt() - 状态分析提示词模板
4. get_routing_decision_prompt() - 路由决策提示词模板
5. get_context_aware_prompt() - 上下文感知提示词模板 (从RouterAgent.py移动)
6. build_context_aware_prompt() - 构建上下文感知提示词 (从RouterAgent.py移动)
7. summarize_existing_info() - 总结状态信息 (从RouterAgent.py移动)

使用方法：
```python
from app.agents.prompts.router_prompts import RouterPrompts

# 构建上下文感知提示词
prompt = RouterPrompts.build_context_aware_prompt(user_input, existing_info)

# 总结状态信息
summary = RouterPrompts.summarize_existing_info(state)
```
"""

from typing import Dict


class RouterPrompts:
    """路由Agent提示词模板类"""

    # LLM分析提示词模板 - 用于引导LLM智能选择和调用工具进行分析
    ANALYSIS_PROMPT = """
请分析当前营销路由情况并智能选择工具进行分析。

## 当前状态
- 用户输入：{userInput}
- 当前步骤：{currentStep}
- 状态摘要：{stateSummary}

## 可用工具
1. **intentAnalysisTool** - 分析用户意图类型（咨询/方案生成/不明确）
   - 参数：userInput (用户输入文本)
   - 返回：JSON格式的意图分析结果

2. **stateAnalysisTool** - 评估营销工作流状态和完整性
   - 参数：stateData (JSON格式的状态数据)
   - 返回：JSON格式的状态分析结果

3. **sufficiencyEvaluationTool** - 评估信息充分性和收集策略
   - 参数：intentType (意图类型), stateAnalysis (状态分析结果)
   - 返回：JSON格式的充分性评估结果

4. **RouterTool** - 基于分析结果做出路由决策
   - 参数：intentAnalysis, stateAnalysis, sufficiencyResult
   - 返回：JSON格式的路由决策结果

## 分析要求
请根据当前情况智能选择和调用工具，最多调用5次工具。
建议按以下顺序调用：
1. 首先调用 intentAnalysisTool 分析用户意图
2. 然后调用 stateAnalysisTool 评估当前状态
3. 接着调用 sufficiencyEvaluationTool 评估信息充分性
4. 最后调用 routingDecisionTool 做出最终路由决策

每次调用工具时，请确保参数格式正确。
"""

    # 意图分析提示词模板 - 用于LLM分析用户输入的营销意图类型
    INTENT_ANALYSIS_PROMPT = """
你是营销意图识别专家，请分析用户输入的营销意图类型。

## 用户输入
{userInput}

## 意图类型定义
请从以下类型中选择最匹配的意图：

1. **planGeneration** - 明确要求生成营销方案
   - 特征：包含"生成"、"制定"、"设计"、"帮我做"等动作词
   - 示例：帮我生成一个信用卡推广方案

2. **consultation** - 咨询类需求，需要指导和建议
   - 特征：包含"了解"、"如何"、"咨询"、"建议"等询问词
   - 示例：我想了解一下营销活动怎么做

3. **unclear** - 意图不明确，需要进一步澄清
   - 特征：简单问候、模糊表达、信息不足
   - 示例：你好、帮我、我想做个活动

## 输出要求
**重要：请严格按照以下JSON格式返回，不要包含任何其他文本或解释**

```json
{{
    "intentType": "planGeneration|consultation|unclear",
    "confidence": 0.8,
    "reasoning": "分析推理过程",
    "keywords": ["关键词1", "关键词2"],
    "inputComplexity": "simple|medium|complex",
    "analysisMethod": "llm_enhanced"
}}
```

**注意事项：**
1. 只返回JSON，不要包含任何前缀或后缀文本
2. 确保所有字符串值都用双引号包围
3. confidence值必须是0-1之间的数字
4. keywords必须是字符串数组
5. 所有字段都必须包含，不能省略
"""

    # 状态分析提示词模板 - 用于LLM分析营销工作流的当前状态
    STATE_ANALYSIS_PROMPT = """
你是营销状态分析专家，请分析当前营销工作流的状态。

## 状态数据
{stateData}

## 分析维度
请从以下维度进行分析：

1. **模块完整性** - 各个信息模块的完整程度
   - userInput: 用户输入是否存在且有意义
   - intentInfo: 意图信息是否完整
   - customerTags: 客户标签是否存在
   - historicalData: 历史数据是否已查询
   - planDraft: 方案草稿是否已生成

2. **工作流进度** - 当前处于哪个阶段
   - initialization: 初始化阶段
   - intentAnalysis: 意图分析阶段
   - customerAnalysis: 客户分析阶段
   - dataCollection: 数据收集阶段
   - planCreation: 方案生成阶段
   - finalization: 完成阶段

3. **信息质量** - 已有信息的质量评估
   - 信息的完整性、准确性、相关性

4. **缺失分析** - 还需要哪些关键信息
   - 识别关键缺失字段
   - 评估缺失信息的重要性

## 输出要求
**重要：请严格按照以下JSON格式返回，不要包含任何其他文本或解释**

```json
{{
    "moduleCompleteness": {{
        "userInput": true,
        "intentInfo": false,
        "customerTags": false,
        "historicalData": false,
        "planDraft": false
    }},
    "informationSufficiency": 0.6,
    "workflowStage": "intentAnalysis",
    "qualityScore": 0.7,
    "analysisMethod": "llm_enhanced"
}}
```

**注意事项：**
1. 只返回JSON，不要包含任何前缀或后缀文本
2. moduleCompleteness中的值必须是布尔值（true/false）
3. informationSufficiency和qualityScore必须是0-1之间的数字
4. workflowStage必须是预定义的阶段名称
5. 所有字段都必须包含，不能省略
"""

    # 路由决策提示词模板 - 用于LLM基于分析结果做出最佳路由决策
    ROUTING_DECISION_PROMPT = """
你是营销路由决策专家，请基于分析结果做出最佳路由决策。

## 分析结果
- **意图分析**：{intentAnalysis}
- **状态分析**：{stateAnalysis}
- **充分性评估**：{sufficiencyResult}

## 可选的下一个节点
请从以下节点中选择最合适的下一步：

1. **INTENTION_AGENT** - 意图识别Agent
   - 适用：意图不明确或需要深度挖掘需求
   - 场景：用户输入模糊、咨询类需求

2. **COMPLETE_INTENT_NODE** - 意图补全节点
   - 适用：意图基本明确但缺少关键信息
   - 场景：需要收集具体的营销目标、预算等

3. **HISTORY_RAG_AGENT** - 历史数据检索Agent
   - 适用：意图明确且信息充足，准备生成方案
   - 场景：方案生成前查询相关历史案例

4. **TAG_CUSTOMER_AGENT** - 客户标签Agent
   - 适用：有基本意图但缺少客户信息
   - 场景：需要分析目标客群特征

5. **COMPLETE_CUSTOMER_NODE** - 客户补全节点
   - 适用：需要收集更多客户相关信息
   - 场景：客户标签不完整

6. **PLAN_CREATOR_AGENT** - 方案生成Agent
   - 适用：所有必要信息已收集完毕
   - 场景：可以直接生成营销方案

7. **COMPLETE_PLAN_NODE** - 方案补全节点
   - 适用：方案已生成但需要优化完善
   - 场景：方案细化和调整

8. **END** - 流程结束
   - 适用：所有任务已完成
   - 场景：方案已生成并确认

## 决策原则
1. **优先级原则**：优先处理缺失的核心信息
2. **逻辑原则**：遵循业务流程逻辑（意图→客户→数据→方案）
3. **效率原则**：避免重复执行已完成的步骤
4. **用户体验原则**：考虑用户交互需求

## 输出要求
请以JSON格式返回路由决策：
```json
{{
    "nextAgent": "INTENTION_AGENT",
    "intentType": "consultation",
    "routingStrategy": "userInteraction",
    "reasoning": "详细的决策推理过程",
    "confidence": 0.8,
    "expectedOutcome": "预期执行结果",
    "fallbackOptions": ["COMPLETE_INTENT_NODE"],
    "executionPriority": "high",
    "riskAssessment": "低风险：决策稳定可靠",
    "decisionMethod": "llm_enhanced"
}}
```
"""

    # 上下文感知的路由分析提示词模板 - 用于根据当前状态信息生成智能的分析提示词
    CONTEXT_AWARE_PROMPT = """你是智能营销路由专家。请根据用户输入和当前状态，智能选择和调用工具来做出最佳路由决策。

## 用户输入
{userInput}

## 当前状态信息
{existingInfo}

## 可用工具
1. stateAssessmentTool - 智能评估当前状态的信息完整度
   参数：currentStateInfo（状态描述）, userInput（用户输入）, stateData（状态数据，可选）

2. intentAnalysisTypeTool - 轻量级意图分析（仅用于路由决策）
   参数：userInput（用户输入文本）

3. routerTool - 基于分析结果做出最终路由决策
   参数：stateAssessment（状态评估结果）, intentAnalysis（意图分析结果）

## 工具调用指南
- stateAssessmentTool：传递状态描述和用户输入，让工具智能分析
- intentAnalysisTypeTool：传递用户原始输入进行意图分析
- routerTool：传递前两个工具的JSON结果进行最终决策

## 用户类型识别要点
- 咨询类用户：使用"了解"、"如何"、"咨询"等词汇，需要引导和建议
- 快速生成类用户：明确要求"生成"、"制定"、"帮我做"，信息相对完整
- 探索类用户：意图模糊，需要逐步明确需求

## 智能执行策略
1. 优先调用stateAssessmentTool评估当前状态，传递用户输入进行内容分析
2. 根据状态评估结果决定是否需要intentAnalysisTypeTool
3. 最后必须调用routerTool做出路由决策
4. 确保传递正确的参数格式给每个工具

请智能选择需要的工具并执行分析。"""

    # LLM意图分析提示词模板 - 用于智能分析用户意图
    LLM_INTENT_ANALYSIS_PROMPT = """分析用户的营销意图，返回JSON格式结果：

用户输入：{userInput}

请分析并返回：
{{
    "intentType": "planGeneration|consultation|exploration",
    "userStyle": "directive|consultative|exploratory",
    "urgency": "high|medium|low",
    "needsDetailedAnalysis": true|false,
    "confidence": 0.8,
    "reasoning": "分析推理过程"
}}

意图类型说明：
- planGeneration: 明确要求生成方案
- consultation: 咨询类需求，需要指导
- exploration: 意图不明确，需要澄清

用户风格说明：
- directive: 直接要求型（"帮我做"）
- consultative: 咨询型（"如何做"）
- exploratory: 探索型（模糊表达）"""

    @staticmethod
    def build_context_aware_prompt(user_input: str, existing_info: str) -> str:
        """
        构建上下文感知的路由分析提示词

        Args:
            user_input: 用户输入文本
            existing_info: 当前状态信息摘要

        Returns:
            str: 格式化的提示词
        """
        return RouterPrompts.CONTEXT_AWARE_PROMPT.format(
            userInput=user_input,
            existingInfo=existing_info
        )

    @staticmethod
    def summarize_existing_info(state: Dict) -> str:
        """
        总结当前已有的状态信息

        分析state中已存在的信息，为路由决策提供上下文

        Args:
            state: 营销状态字典

        Returns:
            str: 状态信息摘要
        """
        summary = []

        # 检查意图信息
        intent_info = state.get('intentInfo')
        if intent_info:
            goal = intent_info.get('goal', '未知')
            activity_type = intent_info.get('activityType', '未知')
            audience = intent_info.get('audience', '未知')
            summary.append(f"✅ 意图信息已存在: 目标={goal}, 类型={activity_type}, 受众={audience}")
        else:
            summary.append("❌ 意图信息缺失")

        # 检查客户标签
        customer_tags = state.get('customerTags')
        if customer_tags:
            summary.append("✅ 客户标签已存在")
        else:
            summary.append("❌ 客户标签缺失")

        # 检查历史数据
        rag_context = state.get('ragContext')
        if rag_context:
            summary.append("✅ 历史数据已查询")
        else:
            summary.append("❌ 历史数据未查询")

        # 检查方案草稿
        plan_draft = state.get('planDraft')
        if plan_draft:
            summary.append("✅ 方案草稿已存在")
        else:
            summary.append("❌ 方案草稿未生成")

        # 检查当前步骤
        current_step = state.get('currentStep', '未知')
        summary.append(f"📍 当前步骤: {current_step}")

        return "\n".join(summary)

    # 便利方法 - 保持向后兼容性（可选）
    @staticmethod
    def get_analysis_prompt() -> str:
        """获取LLM分析提示词模板"""
        return RouterPrompts.ANALYSIS_PROMPT

    @staticmethod
    def get_intent_analysis_prompt() -> str:
        """获取意图分析提示词模板"""
        return RouterPrompts.INTENT_ANALYSIS_PROMPT

    @staticmethod
    def get_state_analysis_prompt() -> str:
        """获取状态分析提示词模板"""
        return RouterPrompts.STATE_ANALYSIS_PROMPT

    @staticmethod
    def get_routing_decision_prompt() -> str:
        """获取路由决策提示词模板"""
        return RouterPrompts.ROUTING_DECISION_PROMPT

    @staticmethod
    def get_context_aware_prompt() -> str:
        """获取上下文感知提示词模板"""
        return RouterPrompts.CONTEXT_AWARE_PROMPT

    @staticmethod
    def get_all_prompts() -> Dict[str, str]:
        """
        获取所有提示词模板的字典
        """
        return {
            "analysisPrompt": RouterPrompts.ANALYSIS_PROMPT,
            "intentAnalysisPrompt": RouterPrompts.INTENT_ANALYSIS_PROMPT,
            "stateAnalysisPrompt": RouterPrompts.STATE_ANALYSIS_PROMPT,
            "routingDecisionPrompt": RouterPrompts.ROUTING_DECISION_PROMPT,
            "contextAwarePrompt": RouterPrompts.CONTEXT_AWARE_PROMPT,
            "llmIntentAnalysisPrompt": RouterPrompts.LLM_INTENT_ANALYSIS_PROMPT
        }


# 为了向后兼容，提供直接访问的变量
ROUTER_PROMPTS = RouterPrompts.get_all_prompts()
