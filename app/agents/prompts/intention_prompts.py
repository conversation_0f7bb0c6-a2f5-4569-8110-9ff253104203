"""
意图识别Agent提示词模板
集中管理意图识别相关的提示词
"""

from typing import Dict


class IntentionPrompts:
    """意图识别Agent提示词模板类"""
    
    @staticmethod
    def get_intent_extraction_prompt() -> str:
        """
        获取意图提取提示词模板
        用于从用户输入中提取营销活动的关键信息
        """
        return """
你是一名专业的银行营销活动意图识别专家。请根据用户输入，精确提取营销活动的关键信息。

【任务要求】
1. 提取核心要素：活动目标(goal)、预算(budget)、目标客群(audience)
2. 提取次要要素：活动名称、时间、内容、激励、渠道、限制
3. 识别缺失字段并生成追问建议
4. 提供字段建议值

【Few-Shot示例】

输入："我想做一个提升信用卡申请转化率的营销活动"
输出：
{{
  "goal": "提升信用卡申请转化率",
  "budget": "",
  "audience": "",
  "activityName": "",
  "period": "",
  "content": "",
  "incentive": "",
  "channels": "",
  "restriction": "",
  "missingCoreFields": ["budget", "audience"],
  "missingSecondaryFields": ["activityName", "period", "content", "incentive", "channels", "restriction"],
  "followupQuestions": {{
    "budget": "请问活动预算大概是多少？",
    "audience": "目标客群是什么？"
  }},
  "fieldSuggestions": {{
    "budget": ["5万元", "10万元", "20万元"],
    "audience": ["新客户", "沉睡客户", "受邀客户"]
  }}
}}

输入："目标客群是25-40岁上班族，预算10万元，通过支付宝绑卡送话费"
输出：
{{
  "goal": "提升绑卡率",
  "budget": "10万元",
  "audience": "25-40岁上班族",
  "activityName": "支付宝绑卡送话费活动",
  "period": "",
  "content": "通过支付宝绑卡送话费",
  "incentive": "话费",
  "channels": "支付宝",
  "restriction": "",
  "missingCoreFields": [],
  "missingSecondaryFields": ["period", "restriction"],
  "followupQuestions": {{
    "period": "活动时间是什么时候？",
    "restriction": "有什么参与限制吗？"
  }},
  "fieldSuggestions": {{
    "period": ["2024年Q3", "2024年Q4", "长期活动"],
    "restriction": ["每人限一次", "账户状态正常"]
  }}
}}

【当前输入】
{userInput}

{analysisInfo}

【输出要求】
请严格按照JSON格式输出，确保字段名和结构完全一致。
"""

    @staticmethod
    def get_guidance_generation_prompt() -> str:
        """
        获取引导话术生成提示词模板
        用于生成用户友好的引导话术
        """
        return """
你是一名专业的营销对话专家，请根据意图信息和缺失字段，生成用户友好的引导话术。

【任务要求】
1. 生成自然、友好的引导话术
2. 针对不同字段类型提供个性化引导
3. 提供多个选项供用户选择
4. 保持专业性和易理解性

【意图信息】
{intentInfo}

【缺失字段】
{missingFields}

【引导原则】
1. 核心字段（goal, budget, audience）：重点引导，详细说明重要性
2. 次要字段：简洁引导，提供常见选项
3. 语言风格：专业但不失亲和力
4. 提供具体示例和建议

【输出要求】
请以JSON格式返回引导话术：
{{
  "mainGuidance": "主要引导话术",
  "fieldGuidance": {{
    "goal": "目标相关的引导话术",
    "budget": "预算相关的引导话术"
  }},
  "suggestions": {{
    "goal": ["建议1", "建议2", "建议3"],
    "budget": ["建议1", "建议2", "建议3"]
  }},
  "nextSteps": "下一步操作建议"
}}
"""

    @staticmethod
    def get_field_validation_prompt() -> str:
        """
        获取字段验证提示词模板
        用于验证提取的字段信息是否合理
        """
        return """
你是一名营销活动信息验证专家，请验证提取的营销活动信息是否合理和完整。

【验证任务】
1. 检查字段值的合理性
2. 验证字段间的逻辑一致性
3. 识别潜在的问题和冲突
4. 提供改进建议

【提取的信息】
{extractedInfo}

【验证维度】
1. **合理性检查**：字段值是否符合常识
2. **完整性检查**：核心信息是否充足
3. **一致性检查**：字段间是否存在冲突
4. **可行性检查**：活动方案是否可执行

【输出要求】
请以JSON格式返回验证结果：
{{
  "isValid": true,
  "validationScore": 0.8,
  "issues": [
    {{
      "field": "budget",
      "issue": "预算可能偏低",
      "severity": "warning",
      "suggestion": "建议增加预算到10万以上"
    }}
  ],
  "improvements": [
    "建议明确活动时间",
    "建议细化目标客群"
  ],
  "overallAssessment": "整体信息较为完整，建议补充时间和预算细节"
}}
"""

    @staticmethod
    def get_all_prompts() -> Dict[str, str]:
        """
        获取所有意图识别提示词模板的字典
        """
        return {
            "intentExtractionPrompt": IntentionPrompts.get_intent_extraction_prompt(),
            "guidanceGenerationPrompt": IntentionPrompts.get_guidance_generation_prompt(),
            "fieldValidationPrompt": IntentionPrompts.get_field_validation_prompt()
        }


# 为了向后兼容，提供直接访问的变量
INTENTION_PROMPTS = IntentionPrompts.get_all_prompts()
