"""
反思工具模块：为各业务Agent提供通用的质量评估与建议能力
"""
from typing import Dict, Any
from app.utils.prompts import reflectionPrompt
from app.llm.LlmFactory import getDefaultLLM
import json

def reflect_with_prompt(reflection_target: str, state_data: dict, quality_criteria: str = "", custom_prompt=None) -> Dict[str, Any]:
    """
    通用反思工具，支持动态加载不同反思目标和Prompt模板
    :param reflection_target: 反思目标（如 '意图识别', '客群分析', '方案生成' 等）
    :param state_data: 需要反思的结构化数据
    :param quality_criteria: 质量标准描述
    :param custom_prompt: 可选，自定义PromptTemplate
    :return: 反思结果（如质量分数、问题、建议等）
    """
    prompt_template = custom_prompt or reflectionPrompt
    prompt = prompt_template.format(
        reflectionTarget=reflection_target,
        stateData=json.dumps(state_data, ensure_ascii=False),
        qualityCriteria=quality_criteria
    )
    llm = getDefaultLLM()
    result = llm.invoke(prompt)
    # 解析LLM输出为dict
    try:
        return json.loads(result.content if hasattr(result, 'content') else str(result))
    except Exception:
        return {"raw": result.content if hasattr(result, 'content') else str(result), "error": "LLM输出无法解析为JSON"}

# 删除未使用的反思函数，只保留通用的 reflect_with_prompt 函数