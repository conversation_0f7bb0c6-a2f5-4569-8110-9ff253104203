"""
Router Agent Tools - 简化版路由Agent工具集合

优化后的RouterAgent工具，包含：
1. LLM增强的意图分析工具 - 智能理解用户意图
2. 简化的状态评估工具 - 快速评估信息完整度
3. 智能路由决策工具 - 基于分析结果做出路由决策
4. 简化的高级分析工具 - 提供额外的分析能力

特点：
- 代码简洁明了，易于维护
- 使用驼峰命名规范
- LLM增强 + 规则兜底的混合模式
- 移除过度设计，保留核心功能

使用方法：
```python
from app.agents.tools.RouterAgentTools import (
    stateAssessmentTool,
    intentAnalysisTypeTool,
    RouterTool,
    analyzeStateTool
)

# 直接使用工具
stateResult = stateAssessmentTool.invoke("意图信息已存在")
intentResult = intentAnalysisTypeTool.invoke("帮我生成营销方案")
routingResult = RouterTool.invoke(stateResult, intentResult)

# 使用高级工具
result = analyzeStateTool.invoke({"stateData": data})
```
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Literal, Annotated
from pydantic import BaseModel, Field

from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig
from app.utils.routingModels import UserIntentAnalysis, MarketingStateAnalysis, RoutingDecision
from app.utils.models import MarketingState
from app.llm.LlmFactory import getDefaultLLM
from app.agents.prompts.router_prompts import RouterPrompts
from app.utils.llm_utils import processLLMJsonResponse
from loguru import logger


# ============================================================================
# 基础路由工具 (从RouterAgent.py提取)
# ============================================================================

# 删除重复的函数，使用下面统一的版本


@tool
def stateAssessmentTool(
    currentStateInfo: str,
    userInput: str = "",
    config: RunnableConfig = None
) -> str:
    """
    智能评估营销状态信息完整度

    优先使用工作流状态标记，补充内容分析

    Args:
        currentStateInfo: 当前状态信息描述
        userInput: 用户原始输入（可选，用于内容分析）
        config: 运行时配置（包含state数据）

    Returns:
        str: JSON格式的评估结果
    """
    try:
        # 1. 从config中获取state数据（最可靠的方式）
        state = None
        if config and "configurable" in config:
            state = config["configurable"].get("marketing_state")

        if state:
            workflow_checks = {
                "hasIntentInfo": bool(state.get("intentInfo")),
                "hasCustomerTags": bool(state.get("customerTags")),
                "hasPlanDraft": bool(state.get("planDraft"))
            }
        else:
            workflow_checks = {}

        # 2. 基于状态描述的标记检查（兜底方案）
        marker_checks = parseStateMarkers(currentStateInfo)

        # 3. 基于用户输入的内容分析（仅作为补充）
        content_checks = analyzeUserInputContent(userInput) if userInput and not any(workflow_checks.values()) else {}

        # 4. 合并检查结果（优先级：直接状态 > 标记检查 > 内容分析）
        final_checks = {
            "hasIntentInfo": workflow_checks.get("hasIntentInfo") or marker_checks.get("hasIntentInfo") or content_checks.get("hasIntentInfo", False),
            "hasCustomerTags": workflow_checks.get("hasCustomerTags") or marker_checks.get("hasCustomerTags") or content_checks.get("hasCustomerTags", False),
            "hasPlanDraft": workflow_checks.get("hasPlanDraft") or marker_checks.get("hasPlanDraft", False)
        }

        # 计算完整度分数
        score = sum(final_checks.values()) / len(final_checks)

        # 识别缺失信息
        missingInfo = []
        if not final_checks["hasIntentInfo"]:
            missingInfo.append("Intent")
        if not final_checks["hasCustomerTags"]:
            missingInfo.append("Customer标签")
        if not final_checks["hasPlanDraft"]:
            missingInfo.append("Plan草稿")
        # 暂时移除历史数据相关检查

        # 添加更多状态信息
        additional_info = {}
        if state:
            additional_info = {
                "currentStep": state.get("currentStep"),
                "userInput": state.get("userInput", "")[:100],  # 截取前100字符
                "hasHistoricalCases": bool(state.get("historicalCases")),
                "hasRagContext": bool(state.get("ragContext"))
            }

        return json.dumps({
            **final_checks,
            "completenessScore": score,
            "missingInfo": missingInfo,
            "recommendation": getRecommendation(score),
            "analysisMethod": "direct_state" if state else determineAnalysisMethod(workflow_checks, marker_checks, content_checks),
            **additional_info
        }, ensure_ascii=False)

    except Exception as e:
        logger.error(f"状态评估失败: {str(e)}")
        return json.dumps({
            "hasIntentInfo": False,
            "hasCustomerTags": False,
            "hasHistoricalData": False,
            "hasPlanDraft": False,
            "completenessScore": 0.0,
            "missingInfo": ["所有信息"],
            "recommendation": "评估失败，使用默认流程"
        }, ensure_ascii=False)


def parseWorkflowState(stateData: str) -> Dict[str, bool]:
    """
    解析工作流状态数据（最可靠的方式）

    Args:
        stateData: JSON格式的状态数据

    Returns:
        Dict: 状态检查结果
    """
    try:
        if not stateData:
            return {}

        state = json.loads(stateData)

        return {
            "hasIntentInfo": bool(state.get("intentInfo")),
            "hasCustomerTags": bool(state.get("customerTags")),
            "hasPlanDraft": bool(state.get("planDraft"))
            # 暂时移除历史数据相关检查
        }
    except Exception as e:
        logger.debug(f"工作流状态解析失败: {str(e)}")
        return {}


def parseStateMarkers(currentStateInfo: str) -> Dict[str, bool]:
    """
    解析状态描述中的标记

    Args:
        currentStateInfo: 状态描述文本

    Returns:
        Dict: 标记检查结果
    """
    # 支持多种标记格式
    positive_markers = {
        "hasIntentInfo": ["意图信息已存在", "✅ 意图信息", "Intent已完成", "意图分析完成"],
        "hasCustomerTags": ["客户标签已存在", "✅ 客户标签", "Customer标签已完成", "客户分析完成"],
        "hasPlanDraft": ["方案草稿已存在", "✅ 方案草稿", "Plan草稿已生成", "方案生成完成"]
        # 暂时移除历史数据相关标记
    }

    negative_markers = {
        "hasIntentInfo": ["无意图信息", "❌ 意图信息", "Intent缺失", "意图信息缺失"],
        "hasCustomerTags": ["无客户标签", "❌ 客户标签", "Customer标签缺失", "客户标签缺失"],
        "hasPlanDraft": ["无方案草稿", "❌ 方案草稿", "Plan草稿缺失", "方案草稿未生成"]
        # 暂时移除历史数据相关标记
    }

    checks = {}
    for key in positive_markers:
        # 如果有明确的否定标记，则为False
        if any(neg in currentStateInfo for neg in negative_markers[key]):
            checks[key] = False
        # 如果有明确的肯定标记，则为True
        elif any(pos in currentStateInfo for pos in positive_markers[key]):
            checks[key] = True
        # 否则无法确定，返回None（由其他方法补充）
        else:
            checks[key] = None

    return {k: v for k, v in checks.items() if v is not None}


def analyzeUserInputContent(userInput: str) -> Dict[str, bool]:
    """
    分析用户输入内容，识别隐含的信息（仅作为补充）

    Args:
        userInput: 用户输入文本

    Returns:
        Dict: 内容分析结果
    """
    if not userInput or len(userInput.strip()) < 10:
        return {}

    analysis = {}

    # 意图信息检查 - 检查是否包含营销目标和活动描述
    intent_indicators = [
        # 活动类型
        "活动", "促销", "推广", "营销", "方案", "计划", "策略",
        # 目标词汇
        "提升", "增加", "获得", "吸引", "扩大", "改善", "优化",
        # 具体描述
        "预算", "时间", "渠道", "奖品", "奖励", "抽奖"
    ]

    intent_score = sum(1 for keyword in intent_indicators if keyword in userInput)
    if intent_score >= 2:  # 至少包含2个相关词汇
        analysis["hasIntentInfo"] = True

    # 客户标签检查 - 检查是否包含目标受众描述
    audience_indicators = [
        # 年龄相关
        "岁", "年龄", "青年", "中年", "老年",
        # 身份相关
        "大学生", "学生", "白领", "职场", "用户", "客户", "消费者",
        # 群体相关
        "群体", "人群", "受众", "目标", "男性", "女性"
    ]

    # 年龄范围模式
    import re
    age_patterns = [r'\d+-\d+岁', r'\d+岁以上', r'\d+岁以下', r'\d+到\d+岁']
    has_age_range = any(re.search(pattern, userInput) for pattern in age_patterns)

    audience_score = sum(1 for keyword in audience_indicators if keyword in userInput)
    if audience_score >= 1 or has_age_range:
        analysis["hasCustomerTags"] = True

    return analysis


def determineAnalysisMethod(workflow_checks: Dict, marker_checks: Dict, content_checks: Dict) -> str:
    """确定使用的分析方法"""
    if workflow_checks:
        return "workflow_state"
    elif marker_checks:
        return "state_markers"
    elif content_checks:
        return "content_analysis"
    else:
        return "default"


def getRecommendation(score: float) -> str:
    """根据完整度分数给出建议"""
    if score >= 0.8:
        return "信息基本完整，可以开始生成方案"
    elif score >= 0.5:
        return "信息部分完整，建议补充关键信息"
    else:
        return "需要补充关键信息"


def performLlmIntentAnalysis(userInput: str) -> Dict[str, Any]:
    """
    使用LLM进行智能意图分析

    Args:
        userInput: 用户输入文本

    Returns:
        Dict: 意图分析结果
    """
    try:
        llm = getDefaultLLM()

        # 使用提示词文件中的模板
        prompt = RouterPrompts.LLM_INTENT_ANALYSIS_PROMPT.format(userInput=userInput)

        # 定义默认值
        default_result = {
            "intentType": "exploration",
            "userStyle": "exploratory",
            "urgency": "medium",
            "needsDetailedAnalysis": True,
            "confidence": 0.7,
            "reasoning": "LLM分析失败，使用默认值"
        }

        # 调用LLM并处理响应
        response = llm.invoke(prompt)
        result = processLLMJsonResponse(response, default_value=default_result)

        # 确保必要字段存在并标准化
        return {
            "intentType": result.get("intentType", "exploration"),
            "userStyle": result.get("userStyle", "exploratory"),
            "urgency": result.get("urgency", "medium"),
            "needsDetailedAnalysis": result.get("needsDetailedAnalysis", True),
            "confidence": result.get("confidence", 0.7),
            "reasoning": result.get("reasoning", "LLM智能分析")
        }

    except Exception as e:
        logger.error(f"LLM意图分析失败: {str(e)}")
        # 兜底：使用简单规则
        return performRuleBasedIntentAnalysis(userInput)


def performRuleBasedIntentAnalysis(userInput: str) -> Dict[str, Any]:
    """
    基于规则的意图分析（兜底方案）

    Args:
        userInput: 用户输入文本

    Returns:
        Dict: 意图分析结果
    """
    # 简化的关键词匹配
    if any(kw in userInput for kw in ["生成", "制定", "帮我做", "创建"]):
        intentType = "planGeneration"
        userStyle = "directive"
    elif any(kw in userInput for kw in ["了解", "如何", "咨询", "建议"]):
        intentType = "consultation"
        userStyle = "consultative"
    else:
        intentType = "exploration"
        userStyle = "exploratory"

    urgency = "high" if any(kw in userInput for kw in ["急", "快", "立即"]) else "medium"

    return {
        "intentType": intentType,
        "userStyle": userStyle,
        "urgency": urgency,
        "needsDetailedAnalysis": intentType == "exploration",
        "confidence": 0.6,
        "reasoning": "基于规则的简单分析"
    }


@tool
def intentAnalysisTypeTool(userInput: str, existingIntentInfo: str = "") -> str:
    """
    LLM增强的用户意图类型分析

    Args:
        userInput: 用户输入文本
        existingIntentInfo: 已存在的意图信息

    Returns:
        str: JSON格式的意图分析结果
    """
    try:
        # 如果已有意图信息，跳过分析
        if existingIntentInfo and "意图信息已存在" in existingIntentInfo:
            return json.dumps({
                "intentType": "existing",
                "userStyle": "unknown",
                "urgency": "medium",
                "needsDetailedAnalysis": False,
                "reasoning": "意图信息已存在"
            }, ensure_ascii=False)

        # 使用LLM进行智能分析
        result = performLlmIntentAnalysis(userInput)
        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        logger.error(f"意图分析失败: {str(e)}")
        return json.dumps({
            "intentType": "exploration",
            "userStyle": "exploratory",
            "urgency": "medium",
            "needsDetailedAnalysis": True,
            "reasoning": "分析失败，使用默认值"
        }, ensure_ascii=False)


def makeSmartRoutingDecision(stateData: Dict[str, Any], intentData: Dict[str, Any]) -> Dict[str, Any]:
    """
    简化的智能路由决策

    Args:
        stateData: 状态数据
        intentData: 意图数据

    Returns:
        Dict: 路由决策结果
    """
    try:
        # 提取关键信息
        completenessScore = stateData.get("completenessScore", 0.0)
        hasIntentInfo = stateData.get("hasIntentInfo", False)
        hasCustomerTags = stateData.get("hasCustomerTags", False)
        hasPlanDraft = stateData.get("hasPlanDraft", False)
        # 暂时移除历史数据相关变量

        intentType = intentData.get("intentType", "exploration")
        userStyle = intentData.get("userStyle", "exploratory")
        urgency = intentData.get("urgency", "medium")

        # 简化的路由逻辑 - 暂时跳过HistoryRAGAgent
        if intentType == "planGeneration" and completenessScore >= 0.5:
            # 方案生成且信息充足，直接生成方案
            nextNode = "PLAN_CREATOR_AGENT"
            reasoning = "信息充足，直接生成方案"
            confidence = 0.9
        elif intentType == "consultation":
            # 咨询类需求
            nextNode = "COMPLETE_INTENT_NODE" if hasIntentInfo else "INTENTION_AGENT"
            reasoning = "咨询类用户，提供引导建议"
            confidence = 0.8
        elif urgency == "high" and completenessScore >= 0.3:
            # 紧急需求，降低要求
            nextNode = "PLAN_CREATOR_AGENT"
            reasoning = "紧急需求，快速生成方案"
            confidence = 0.6
        else:
            # 默认：从意图分析开始
            nextNode = "INTENTION_AGENT"
            reasoning = "信息不足或意图不明确，从意图分析开始"
            confidence = 0.7

        return {
            "nextNode": nextNode,                    # 下一个要执行的Agent节点 (如: INTENTION_AGENT, TAG_CUSTOMER_AGENT, PLAN_CREATOR_AGENT, END)
            "confidence": confidence,                # 路由决策的置信度 (0.0-1.0, 越高表示决策越可靠)
            "reasoning": reasoning,                  # 路由决策的推理过程和原因说明
            "userStyle": userStyle,                  # 用户交互风格 (directive: 直接要求型, consultative: 咨询型, exploratory: 探索型)
            "strategy": "simplified",               # 路由策略类型 (simplified: 简化策略, intelligent: 智能策略, fallback: 兜底策略)
            "urgency": urgency,                     # 用户需求的紧急程度 (high: 高, medium: 中, low: 低)
            "completenessScore": completenessScore  # 当前信息完整度分数 (0.0-1.0, 1.0表示信息完全充足)
        }

    except Exception as e:
        logger.error(f"路由决策失败: {str(e)}")
        return {
            "nextNode": "INTENTION_AGENT",          # 下一个要执行的Agent节点 - 默认从意图分析开始
            "confidence": 0.5,                      # 路由决策的置信度 - 中等置信度，因为是兜底逻辑
            "reasoning": "决策失败，使用默认路由",      # 路由决策的推理过程 - 说明这是错误恢复
            "userStyle": "unknown",                 # 用户交互风格 - 未知，因为分析失败
            "strategy": "fallback"                  # 路由策略类型 - 兜底策略
        }


@tool
def routerTool(
    stateAssessment: str = "",
    intentAnalysis: str = "",
    config: RunnableConfig = None
) -> str:
    """
    基于状态和意图分析做出路由决策

    Args:
        stateAssessment: JSON格式的状态评估结果
        intentAnalysis: JSON格式的意图分析结果
        config: 运行时配置（包含state数据用于检查真实Agent执行结果）

    Returns:
        str: JSON格式的路由决策结果
    """
    try:
        # 解析输入数据
        stateData = json.loads(stateAssessment) if stateAssessment else {}
        intentData = json.loads(intentAnalysis) if intentAnalysis else {}

        # 🔥 关键修复：检查真实的Agent执行结果
        state = None
        if config and "configurable" in config:
            state = config["configurable"].get("marketing_state")

        if state:
            # 检查真实的Agent执行结果
            hasRealIntentInfo = bool(state.get("intentInfo", {}).get("goal"))
            hasRealCustomerTags = bool(state.get("customerTags", {}).get("tags"))

            # 严格的工作流路由逻辑
            if not hasRealIntentInfo:
                decision = {
                    "nextNode": "INTENTION_AGENT",
                    "confidence": 0.9,
                    "reasoning": "缺少IntentionAgent执行结果，需要意图分析",
                    "strategy": "strict_workflow"
                }
            elif not hasRealCustomerTags:
                decision = {
                    "nextNode": "TAG_CUSTOMER_AGENT",
                    "confidence": 0.9,
                    "reasoning": "缺少TagCustomerAgent执行结果，需要客群分析",
                    "strategy": "strict_workflow"
                }
            else:
                # 基础分析完成，生成方案
                decision = {
                    "nextNode": "PLAN_CREATOR_AGENT",
                    "confidence": 0.9,
                    "reasoning": "意图和客群分析完成，开始生成方案",
                    "strategy": "strict_workflow"
                }
        else:
            # 兜底：使用原有逻辑
            decision = makeSmartRoutingDecision(stateData, intentData)

        return json.dumps(decision, ensure_ascii=False)

    except Exception as e:
        logger.error(f"路由决策失败: {str(e)}")
        return json.dumps({
            "nextNode": "INTENTION_AGENT",          # 下一个要执行的Agent节点 - 默认从意图分析开始
            "confidence": 0.5,                      # 路由决策的置信度 - 中等置信度，因为是兜底逻辑
            "reasoning": "决策失败，使用默认路由",      # 路由决策的推理过程 - 说明这是错误恢复
            "strategy": "fallback"                  # 路由策略类型 - 兜底策略
        }, ensure_ascii=False)


# ============================================================================
# 高级路由工具 (简化版)
# ============================================================================


@tool
def analyzeStateTool(stateData: Dict[str, Any]) -> Dict[str, Any]:
    """
    简化的营销状态分析工具

    Args:
        stateData: 当前状态数据

    Returns:
        Dict: 状态分析结果
    """
    try:
        # 使用基础规则分析
        moduleCompleteness = {
            "userInput": bool(stateData.get("userInput")),
            "intentInfo": bool(stateData.get("intentInfo", {}).get("goal")),
            "customerTags": bool(stateData.get("customerTags", {}).get("tags")),
            "historicalData": bool(stateData.get("historicalCases")),
            "planDraft": bool(stateData.get("planDraft", {}).get("content"))
        }

        completedCount = sum(moduleCompleteness.values())
        totalCount = len(moduleCompleteness)
        overallCompleteness = completedCount / totalCount

        missingModules = [k for k, v in moduleCompleteness.items() if not v]

        return {
            "moduleCompleteness": moduleCompleteness,
            "overallCompleteness": overallCompleteness,
            "missingModules": missingModules,
            "qualityScore": overallCompleteness * 0.8,
            "recommendation": getRecommendation(overallCompleteness)
        }

    except Exception as e:
        logger.error(f"状态分析失败: {str(e)}")
        return {
            "moduleCompleteness": {},
            "overallCompleteness": 0.0,
            "missingModules": ["所有模块"],
            "qualityScore": 0.0,
            "recommendation": "分析失败"
        }


# 高级工具已简化，使用基础工具即可满足需求


# 优化完成：保留核心功能，移除过度设计
