"""
LLM响应处理工具方法

提供简单的LLM响应清理和JSON解析功能。

Author: Marketing AI Team
Version: 1.0.0
Date: 2025-07-13
"""

import json
import re
from typing import Dict, Any, Optional


def extractResponseContent(response) -> str:
    """
    从LLM响应中提取文本内容

    Args:
        response: LLM响应对象

    Returns:
        str: 提取的文本内容
    """
    if hasattr(response, 'content'):
        return response.content
    else:
        return str(response)


def cleanJsonResponse(response_text: str) -> str:
    """
    清理LLM响应中的JSON内容，移除Markdown代码块标记

    Args:
        response_text: 原始响应文本

    Returns:
        str: 清理后的JSON文本
    """
    if not response_text or not response_text.strip():
        return ""

    cleaned = response_text.strip()

    # 移除Markdown代码块标记
    if cleaned.startswith("```json"):
        cleaned = cleaned[7:]  # 移除 ```json
    elif cleaned.startswith("```"):
        cleaned = cleaned[3:]  # 移除 ```

    if cleaned.endswith("```"):
        cleaned = cleaned[:-3]  # 移除结尾的 ```

    return cleaned.strip()


def parseJsonResponse(response_text: str, default_value: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    解析LLM响应中的JSON内容，支持多种格式

    Args:
        response_text: LLM的原始响应文本
        default_value: 解析失败时的默认值

    Returns:
        Dict[str, Any]: 解析后的JSON字典
    """
    if not response_text or not response_text.strip():
        if default_value is not None:
            return default_value
        raise ValueError("响应文本为空")

    # 1. 尝试直接解析（最简单的情况）
    try:
        return json.loads(response_text.strip())
    except json.JSONDecodeError:
        pass

    # 2. 清理Markdown标记后再次尝试
    try:
        cleaned = cleanJsonResponse(response_text)
        return json.loads(cleaned)
    except json.JSONDecodeError:
        pass

    # 3. 使用正则表达式查找JSON内容
    json_patterns = [
        r'```json\s*(\{.*?\})\s*```',  # ```json {content} ```
        r'```\s*(\{.*?\})\s*```',      # ``` {content} ```
        r'```json\s*(\[.*?\])\s*```',  # ```json [content] ```
        r'```\s*(\[.*?\])\s*```',      # ``` [content] ```
        r'\{.*?\}',                    # 直接匹配JSON对象
        r'\[.*?\]'                     # 直接匹配JSON数组
    ]

    for pattern in json_patterns:
        matches = re.findall(pattern, response_text, re.DOTALL)
        for match in matches:
            try:
                return json.loads(match.strip())
            except json.JSONDecodeError:
                continue

    # 4. 如果都失败了，返回默认值或抛出异常
    if default_value is not None:
        return default_value

    raise ValueError(f"无法从响应中提取有效的JSON内容: {response_text[:200]}...")


def processLLMJsonResponse(response, default_value: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    处理LLM响应并解析为JSON的一站式方法

    Args:
        response: LLM响应对象
        default_value: 解析失败时的默认值

    Returns:
        Dict[str, Any]: 解析后的JSON字典
    """
    # 提取响应内容
    content = extractResponseContent(response)

    # 解析JSON
    return parseJsonResponse(content, default_value)
