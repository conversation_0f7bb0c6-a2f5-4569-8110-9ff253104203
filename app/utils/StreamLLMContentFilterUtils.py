class StreamTagContentFilter:
    """简化的流式标签内容过滤器"""

    def __init__(self, tag_name: str):
        self.start_tag = f"<{tag_name}>"
        self.end_tag = f"</{tag_name}>"
        self.reset()

    def reset(self):
        """重置过滤器状态"""
        self.buffer = ""
        self.inside_tag = False
        self.content_start = 0  # 标签内容在buffer中的起始位置

    def filter_content(self, content: str) -> str:
        """
        过滤内容，返回标签内的内容片段

        Args:
            content: 当前接收到的内容片段
        Returns:
            str: 标签内的内容片段，如果不在标签内则返回空字符串
        """
        if not content:
            return ""

        old_buffer_len = len(self.buffer)
        self.buffer += content

        # 如果还没进入标签，检查开始标签
        if not self.inside_tag:
            start_idx = self.buffer.find(self.start_tag)
            if start_idx >= 0:
                self.inside_tag = True
                self.content_start = start_idx + len(self.start_tag)

        # 如果在标签内，检查结束标签
        if self.inside_tag:
            end_idx = self.buffer.find(self.end_tag, self.content_start)
            if end_idx >= 0:
                # 找到结束标签
                tag_content = self.buffer[self.content_start:end_idx]

                # 计算当前chunk在标签内容中的部分
                current_start_in_tag = max(0, old_buffer_len - self.content_start)

                if current_start_in_tag < len(tag_content):
                    result = tag_content[current_start_in_tag:]
                    self.reset()
                    return result
                else:
                    self.reset()
                    return ""
            else:
                # 没有完整的结束标签，但可能有部分结束标签
                # 检查是否有部分结束标签，如果有，只返回结束标签之前的内容
                tag_content_so_far = self.buffer[self.content_start:]

                # 查找可能的部分结束标签
                for i in range(1, len(self.end_tag)):
                    partial_end = self.end_tag[:i]
                    if tag_content_so_far.endswith(partial_end):
                        # 找到部分结束标签，移除它
                        tag_content_so_far = tag_content_so_far[:-i]
                        break

                # 计算当前chunk在标签内容中的部分
                current_start_in_tag = max(0, old_buffer_len - self.content_start)

                if current_start_in_tag < len(tag_content_so_far):
                    return tag_content_so_far[current_start_in_tag:]
                else:
                    return ""

        return ""


# 便捷函数，去掉过度设计的工厂类
def create_reasoning_filter():
    """创建 reasoning 标签过滤器"""
    return StreamTagContentFilter("reasoning")

def create_result_filter():
    """创建 result 标签过滤器"""
    return StreamTagContentFilter("result")

def create_think_filter():
    """创建 think 标签过滤器"""
    return StreamTagContentFilter("think")


class StreamContentFilter:
    """流式内容过滤器 - 简洁版本"""

    def __init__(self):
        self.reset()

    def reset(self):
        """重置状态"""
        self.reasoningFilter = create_reasoning_filter()
        self.resultFilter = create_result_filter()
        self.currentPhase = 'reasoning'
        self.hasReasoning = False
        self.hasResult = False

    def filterContent(self, content: str) -> tuple[str, str]:
        """
        过滤内容
        返回: (过滤后内容, 内容类型)
        """
        if not content:
            return "", ""

        # 过滤工具调用
        if "<tool_call>" in content and "</tool_call>" not in content:
            return "", ""
        if "</tool_call>" in content:
            content = content[content.find("</tool_call>") + 8:]
            if not content:
                return "", ""

        if self.currentPhase == 'reasoning':
            result = self.reasoningFilter.filter_content(content)
            if result:
                self.hasReasoning = True
            if self.hasReasoning and not self.reasoningFilter.inside_tag:
                self.currentPhase = 'result'
                self.hasReasoning = False
            return result, "reasoning" if result else ""

        elif self.currentPhase == 'result':
            result = self.resultFilter.filter_content(content)
            if result:
                output = ""
                self.hasResult = True
                if "<result>" in content:
                    output += "<result>"
                output += result
                if not self.resultFilter.inside_tag:
                    output += "</result>"
                    self.currentPhase = 'done'
                return output, "result"
            elif self.hasResult and not self.resultFilter.inside_tag:
                self.currentPhase = 'done'
                self.hasResult = False
                return "", ""
        return "", ""

    def resetForNewCall(self):
        """为新LLM调用重置"""
        self.reasoningFilter.reset()
        self.resultFilter.reset()
        self.currentPhase = 'reasoning'
        self.hasReasoning = False
        self.hasResult = False


# 测试代码
if __name__ == "__main__":
    print("=== 流式标签内容过滤器测试 ===\n")

    # 测试1：跨chunk的reasoning标签过滤
    print("测试1：跨chunk的reasoning标签过滤")
    print("-" * 40)
    filter1 = create_reasoning_filter()

    test_chunks1 = [
        "这是一些前置内容",
        "<reason",
        "ing>这是推理",
        "内容的第一部分",
        "这是推理内容的第二部分</reason",
        "ing>这是后续内容"
    ]

    result1 = []
    for i, chunk in enumerate(test_chunks1):
        filtered = filter1.filter_content(chunk)
        result1.append(filtered)
        print(f"Chunk {i}: '{chunk}' -> '{filtered}'")

    expected1 = "这是推理内容的第一部分这是推理内容的第二部分"
    actual1 = "".join(result1)
    print(f"期望结果: '{expected1}'")
    print(f"实际结果: '{actual1}'")
    print(f"测试1结果: {'✅ 通过' if actual1 == expected1 else '❌ 失败'}\n")

    # 测试2：完整标签在单个chunk中
    print("测试2：完整标签在单个chunk中")
    print("-" * 40)
    filter2 = create_reasoning_filter()

    test_chunks2 = [
        "前置内容<reasoning>完整的推理内容</reasoning>后续内容"
    ]

    result2 = []
    for i, chunk in enumerate(test_chunks2):
        filtered = filter2.filter_content(chunk)
        result2.append(filtered)
        print(f"Chunk {i}: '{chunk}' -> '{filtered}'")

    expected2 = "完整的推理内容"
    actual2 = "".join(result2)
    print(f"期望结果: '{expected2}'")
    print(f"实际结果: '{actual2}'")
    print(f"测试2结果: {'✅ 通过' if actual2 == expected2 else '❌ 失败'}\n")

    # 测试3：多个标签
    print("测试3：多个标签")
    print("-" * 40)
    filter3 = create_reasoning_filter()

    test_chunks3 = [
        "前置<reasoning>第一段</reasoning>中间<reasoning>第二段</reasoning>后续"
    ]

    result3 = []
    for i, chunk in enumerate(test_chunks3):
        filtered = filter3.filter_content(chunk)
        result3.append(filtered)
        print(f"Chunk {i}: '{chunk}' -> '{filtered}'")

    expected3 = "第一段"  # 只提取第一个标签的内容
    actual3 = "".join(result3)
    print(f"期望结果: '{expected3}'")
    print(f"实际结果: '{actual3}'")
    print(f"测试3结果: {'✅ 通过' if actual3 == expected3 else '❌ 失败'}\n")

