#!/usr/bin/env python3
"""
流式输出处理工具

提供统一的LLM流式输出处理和事件发送功能
"""

import logging
from typing import Callable, Optional, Dict, Any, Generator, Tuple
from datetime import datetime


class StreamHelper:
    """
    流式输出处理助手
    
    统一处理LLM流式输出，自动提取runId，发送标准化事件
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化流式助手
        
        Args:
            logger: 日志记录器，如果为None则创建默认logger
        """
        self.logger = logger or logging.getLogger(__name__)
    
    def process_llm_stream(
        self,
        llm_stream: Generator,
        callback: Optional[Callable] = None,
        agent_name: str = "UNKNOWN_AGENT",
        event_type: str = "generating",
        extract_run_id: bool = True,
        accumulate_response: bool = True
    ) -> Tuple[str, Optional[str]]:
        """
        处理LLM流式输出的通用方法
        
        Args:
            llm_stream: LLM流式生成器 (如 self.llm.stream(prompt))
            callback: 事件回调函数
            agent_name: Agent名称
            event_type: 事件类型 (默认: "generating")
            extract_run_id: 是否提取runId
            accumulate_response: 是否累积完整响应
            
        Returns:
            Tuple[str, Optional[str]]: (完整响应, runId)
        """
        full_response = ""
        current_run_id = None
        chunk_count = 0
        
        try:
            self.logger.debug(f"开始处理{agent_name}的流式输出")
            
            for chunk in llm_stream:
                chunk_count += 1
                
                # 提取chunk内容
                content = self._extract_chunk_content(chunk)
                if not content:
                    continue
                
                # 累积完整响应
                if accumulate_response:
                    full_response += content
                
                # 提取runId (只在第一个chunk时提取)
                if extract_run_id and not current_run_id:
                    current_run_id = self._extract_run_id(chunk)
                
                # 发送流式事件
                if callback:
                    self._send_stream_event(
                        callback=callback,
                        content=content,
                        agent_name=agent_name,
                        event_type=event_type,
                        run_id=current_run_id,
                        chunk_index=chunk_count
                    )
            
            self.logger.debug(f"{agent_name}流式输出完成: {chunk_count}个chunks, runId={current_run_id}")
            
        except Exception as e:
            self.logger.error(f"{agent_name}流式处理失败: {str(e)}")
            # 发送错误事件
            if callback:
                self._send_error_event(callback, agent_name, str(e))
            raise
        
        return full_response, current_run_id
    
    def _extract_chunk_content(self, chunk) -> str:
        """
        从chunk中提取内容
        
        Args:
            chunk: LLM chunk对象
            
        Returns:
            str: 提取的内容
        """
        # 尝试多种可能的内容属性
        for attr in ['content', 'text', 'data']:
            if hasattr(chunk, attr):
                value = getattr(chunk, attr)
                if value:
                    return str(value)
        
        # 如果chunk本身就是字符串
        if isinstance(chunk, str):
            return chunk
            
        return ""
    
    def _extract_run_id(self, chunk) -> Optional[str]:
        """
        从chunk中提取runId
        
        Args:
            chunk: LLM chunk对象
            
        Returns:
            Optional[str]: 提取的runId
        """
        # 尝试多种可能的ID属性
        for attr in ['id', 'run_id', 'request_id', 'session_id']:
            if hasattr(chunk, attr):
                value = getattr(chunk, attr)
                if value:
                    return str(value)
        
        return None
    
    def _send_stream_event(
        self,
        callback: Callable,
        content: str,
        agent_name: str,
        event_type: str,
        run_id: Optional[str] = None,
        chunk_index: int = 0
    ):
        """
        发送流式事件
        
        Args:
            callback: 回调函数
            content: 内容
            agent_name: Agent名称
            event_type: 事件类型
            run_id: 运行ID
            chunk_index: chunk索引
        """
        event_data = {
            "type": event_type,
            "content": content,
            "agent": agent_name,
            "chunkIndex": chunk_index,
            "timestamp": int(datetime.now().timestamp() * 1000)
        }
        
        # 只有当runId存在时才添加
        if run_id:
            event_data["runId"] = run_id
        
        try:
            callback(event_data)
        except Exception as e:
            self.logger.error(f"发送流式事件失败: {str(e)}")
    
    def _send_error_event(self, callback: Callable, agent_name: str, error_message: str):
        """
        发送错误事件
        
        Args:
            callback: 回调函数
            agent_name: Agent名称
            error_message: 错误信息
        """
        error_event = {
            "type": "error",
            "message": f"{agent_name}流式处理失败: {error_message}",
            "agent": agent_name,
            "timestamp": int(datetime.now().timestamp() * 1000)
        }
        
        try:
            callback(error_event)
        except Exception as e:
            self.logger.error(f"发送错误事件失败: {str(e)}")


# 创建全局实例
stream_helper = StreamHelper()


def process_agent_stream(
    llm_stream: Generator,
    callback: Optional[Callable] = None,
    agent_name: str = "UNKNOWN_AGENT",
    **kwargs
) -> Tuple[str, Optional[str]]:
    """
    便捷的Agent流式处理函数
    
    Args:
        llm_stream: LLM流式生成器
        callback: 事件回调函数
        agent_name: Agent名称
        **kwargs: 其他参数传递给process_llm_stream
        
    Returns:
        Tuple[str, Optional[str]]: (完整响应, runId)
    """
    return stream_helper.process_llm_stream(
        llm_stream=llm_stream,
        callback=callback,
        agent_name=agent_name,
        **kwargs
    )


# === 业务特定的流式处理函数 ===

def process_plan_generation_stream(
    llm_stream: Generator,
    callback: Optional[Callable] = None
) -> Tuple[str, Optional[str]]:
    """
    方案生成专用的流式处理
    
    Args:
        llm_stream: LLM流式生成器
        callback: 事件回调函数
        
    Returns:
        Tuple[str, Optional[str]]: (完整响应, runId)
    """
    return process_agent_stream(
        llm_stream=llm_stream,
        callback=callback,
        agent_name="PLAN_CREATOR_AGENT",
        event_type="generating"
    )


def process_analysis_stream(
    llm_stream: Generator,
    callback: Optional[Callable] = None,
    agent_name: str = "ANALYSIS_AGENT"
) -> Tuple[str, Optional[str]]:
    """
    分析类Agent专用的流式处理
    
    Args:
        llm_stream: LLM流式生成器
        callback: 事件回调函数
        agent_name: Agent名称
        
    Returns:
        Tuple[str, Optional[str]]: (完整响应, runId)
    """
    return process_agent_stream(
        llm_stream=llm_stream,
        callback=callback,
        agent_name=agent_name,
        event_type="analyzing"
    )


def process_thinking_stream(
    llm_stream: Generator,
    callback: Optional[Callable] = None,
    agent_name: str = "THINKING_AGENT"
) -> Tuple[str, Optional[str]]:
    """
    思考过程专用的流式处理
    
    Args:
        llm_stream: LLM流式生成器
        callback: 事件回调函数
        agent_name: Agent名称
        
    Returns:
        Tuple[str, Optional[str]]: (完整响应, runId)
    """
    return process_agent_stream(
        llm_stream=llm_stream,
        callback=callback,
        agent_name=agent_name,
        event_type="thinking"
    )
