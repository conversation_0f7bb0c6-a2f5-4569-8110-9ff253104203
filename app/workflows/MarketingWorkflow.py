"""
智能营销工作流，基于LangGraph的多Agent协作系统

适用于API服务/前后端分离/线上部署场景。
"""

import sys
import os

from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import interrupt

from app.core.checkpointer import getDefaultSaver

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import traceback
import logging
import uuid
import asyncio
import re
from langgraph.graph import StateGraph, END
from typing import Dict, Any, Optional, AsyncGenerator, List, Generator

from app.utils.models import MarketingState
from datetime import datetime
from pydantic import BaseModel, Field
from typing import Optional

# 导入所有Agent
from app.agents.RouterAgent import RouterAgent
from app.agents.HistoryRAGAgent import HistoryRAGAgent
from app.agents.IntentionAgent import IntentionAgent
from app.agents.TagCustomerAgent import TagCustomerAgent
from app.agents.PlanCreatorAgent import PlanCreatorAgent
from loguru import logger as loguru_logger
from app.utils.stream_events import (
    StreamEventBuilder,
    StreamEventTypes,
    BusinessContentTypes,
    WorkflowStatus,
    NodeStatus,
    getNodeDisplayName,
    getNodeIndex
)
from app.utils.StreamLLMContentFilterUtils import (
    StreamTagContentFilter,
    create_reasoning_filter,
    create_result_filter,
    create_think_filter,
    StreamContentFilter
)


# 统一的节点显示名称映射
def getNodeDisplayName(nodeId: str) -> str:
    """
    获取节点的用户友好显示名称

    Args:
        nodeId: 节点ID

    Returns:
        str: 显示名称
    """
    displayNames = {
        "ROUTER_AGENT": "智能路由",
        "INTENTION_AGENT": "意图识别",
        "TAG_CUSTOMER_AGENT": "客群分析",
        "HISTORY_RAG_AGENT": "历史数据检索",
        "PLAN_CREATOR_AGENT": "方案生成",
        "COMPLETE_INTENT_NODE": "意图补全",
        "COMPLETE_CUSTOMER_NODE": "客群补全",
        "COMPLETE_PLAN_NODE": "方案补全",
        "END": "流程结束"
    }
    return displayNames.get(nodeId, nodeId)


def shouldShowToUser(content: str) -> bool:
    """
    判断内容是否应该展示给用户

    Args:
        content: 要检查的内容

    Returns:
        bool: 是否应该展示给用户
    """
    if not content or len(content.strip()) < 3:
        return False

    contentLower = content.lower().strip()

    # 过滤技术关键词
    techKeywords = {
        'json', 'nextNode', 'AGENT', 'intentType', 'confidence',
        'reasoning', 'threadId', 'messageId', 'conversationId'
    }

    if any(keyword.lower() in contentLower for keyword in techKeywords):
        return False

    # 过滤JSON结构
    if any(char in content for char in ['{', '}', '":', '","']):
        return False

    return True


def getNodeIndex(nodeId: str) -> int:
    """
    获取节点的执行顺序索引

    Args:
        nodeId: 节点ID

    Returns:
        int: 节点索引
    """
    nodeOrder = {
        "ROUTER_AGENT": 1,
        "INTENTION_AGENT": 2,
        "TAG_CUSTOMER_AGENT": 3,
        "HISTORY_RAG_AGENT": 4,
        "PLAN_CREATOR_AGENT": 5,
        "COMPLETE_INTENT_NODE": 2,
        "COMPLETE_CUSTOMER_NODE": 3,
        "COMPLETE_PLAN_NODE": 5,
        "END": 6
    }
    return nodeOrder.get(nodeId, 0)


def generateFriendlyMessage(nodeId: str, stage: str = 'start') -> str:
    """
    生成用户友好的进度消息

    Args:
        nodeId: 节点ID
        stage: 阶段 ('start' 或 'complete')

    Returns:
        str: 友好的消息
    """
    messages = {
        'ROUTER_AGENT': {
            'start': '正在分析您的营销需求...',
            'complete': '需求分析完成'
        },
        'INTENTION_AGENT': {
            'start': '正在深入理解您的营销目标...',
            'complete': '营销目标分析完成'
        },
        'TAG_CUSTOMER_AGENT': {
            'start': '正在分析目标客群特征...',
            'complete': '客群分析完成'
        },
        'HISTORY_RAG_AGENT': {
            'start': '正在查找相关成功案例...',
            'complete': '历史案例检索完成'
        },
        'PLAN_CREATOR_AGENT': {
            'start': '正在制定营销方案...',
            'complete': '营销方案制定完成'
        },
        'COMPLETE_INTENT_NODE': {
            'start': '需要补充意图信息...',
            'complete': '意图信息补充完成'
        },
        'COMPLETE_CUSTOMER_NODE': {
            'start': '需要补充客群信息...',
            'complete': '客群信息补充完成'
        },
        'COMPLETE_PLAN_NODE': {
            'start': '需要完善营销方案...',
            'complete': '营销方案制定完成'
        }
    }

    nodeMessages = messages.get(nodeId, {})
    return nodeMessages.get(stage, f'{getNodeDisplayName(nodeId)}执行中...')





class MarketingWorkflow:
    """
    智能营销工作流

    基于LangGraph的多Agent协作系统，实现从用户输入到营销方案生成的完整流程。
    适用于API服务/前后端分离/线上部署场景。
    """

    def __init__(self):
        """
        初始化营销工作流（API服务模式，不启用本地交互）。
        包括：Agent初始化、工作流结构构建、状态管理器配置。
        支持短期记忆（内存）和长期记忆（Redis）。
        """
        self.logger = loguru_logger
        self.initializeAgents()
        self.workflow = self.buildWorkflow()
        # 配置checkpointer - 从环境变量读取配置
        try:
            self.checkpointer = getDefaultSaver()  # 自动从.env读取CHECKPOINTER_MODE
            self.logger.info(f"checkpointer初始化成功的类型: {type(self.checkpointer).__name__}")
        except Exception as e:
            self.checkpointer = MemorySaver()
            self.logger.warning(f"checkpointerFallbackToMemory: {str(e)}")

        # 编译工作流 - 添加更好的错误处理和回调管理
        try:
            # 初始化回调管理器
            self._active_callbacks = {}

            self.app = self.workflow.compile(checkpointer=self.checkpointer)
            self.logger.info("🔄 营销工作流编译成功")
        except Exception as e:
            self.logger.error(f"❌ 营销工作流编译失败: {str(e)}")
            # 如果编译失败，尝试不使用checkpointer
            try:
                self.app = self.workflow.compile()
                self.logger.warning("⚠️ 使用无状态模式编译工作流")
            except Exception as e2:
                self.logger.error(f"❌ 无状态模式编译也失败: {str(e2)}")
                raise e2

        # 🔥 初始化当前执行节点跟踪变量
        self._current_executing_node = None

        # 初始化流式内容过滤器
        self.contentFilter = StreamContentFilter()

        self.logger.info("🔄 营销工作流初始化完成 - 运行模式: API服务模式, 状态管理: 内存存储, 工作流编译: 完成")

    def initializeAgents(self) -> None:
        """
        初始化所有Agent实例，并为后续扩展预留接口。
        包括：增强版路由Agent、意图识别Agent、客群标签Agent、方案生成Agent、历史数据检索Agent等。
        """
        try:
            # 使用路由Agent作为唯一路由器
            self.routerAgent = RouterAgent(enableLlmRouting=True)  # 路由Agent

            # 历史数据检索Agent
            self.historyRAGAgent = HistoryRAGAgent()  # 负责历史数据检索和分析

            # 其他Agent保持不变
            self.intentAgent = IntentionAgent()  # 负责意图识别与补全
            self.customerAgent = TagCustomerAgent()  # 负责客群标签补全
            self.planGenAgent = PlanCreatorAgent()  # 负责方案生成

            self.logger.info("🤖 所有Agent实例初始化完成 - 路由Agent: RouterAgent (LLM增强版), 历史检索: HistoryRAGAgent, 意图识别: IntentionAgent, 客户标签: TagCustomerAgent, 方案生成: PlanCreatorAgent")
        except Exception as e:
            self.logger.error(f"Agent初始化失败: {str(e)}")
            raise

    def buildWorkflow(self) -> StateGraph:
        """
        构建营销工作流，所有节点和边集中注册，结构与Mermaid流程图完全一致。
        节点包括：核心Agent节点、补全节点、历史数据检索节点、END节点。
        """
        workflow = StateGraph(MarketingState)
        # 1. 注册所有节点（核心Agent+补全节点）
        workflow.add_node("ROUTER_AGENT", self.createNodeWrapper("ROUTER_AGENT", self.routerAgent.execute))
        workflow.add_node("HISTORY_RAG_AGENT", self.createNodeWrapper("HISTORY_RAG_AGENT", self.historyRAGAgent.execute))
        workflow.add_node("INTENTION_AGENT", self.createNodeWrapper("INTENTION_AGENT", self.intentAgent.execute))
        workflow.add_node("COMPLETE_INTENT_NODE", self.createNodeWrapper("COMPLETE_INTENT_NODE", self.completeIntentNode))
        workflow.add_node("TAG_CUSTOMER_AGENT", self.createNodeWrapper("TAG_CUSTOMER_AGENT", self.customerAgent.execute))
        workflow.add_node("COMPLETE_CUSTOMER_NODE", self.createNodeWrapper("COMPLETE_CUSTOMER_NODE", self.completeCustomerNode))
        workflow.add_node("PLAN_CREATOR_AGENT", self.createNodeWrapper("PLAN_CREATOR_AGENT", self.planGenAgent.execute))
        workflow.add_node("COMPLETE_PLAN_NODE", self.createNodeWrapper("COMPLETE_PLAN_NODE", self.completePlanNode))
        # 终止节点：返回完整的执行结果
        def endNode(state):
            planDraft = state.get("planDraft", {})
            workflowStatus = state.get("workflowStatus", "completed")

            return {
                "success": True,
                "workflowStatus": workflowStatus,
                "planDraft": planDraft,
                "nextAction": state.get("nextAction", "none"),
                "message": "方案生成完成，请查看并确认" if workflowStatus == "plan_generated" else "工作流执行完成"
            }

        workflow.add_node("END", endNode)
        workflow.set_entry_point("ROUTER_AGENT")  # 设置入口节点
        # 2. 注册所有边（流程图结构，条件分流）
        # 增强版RouterAgent分流：基于智能路由决策
        def routerNextSelector(state):
            try:
                # 获取增强版路由决策结果 - RouterAgent将结果直接存储在state根级别
                nextAgent = state.get("nextNode")
                routingConfidence = state.get("routingConfidence", 0.0)
                routingReasoning = state.get("routingReasoning", "未知原因")

                # 验证路由决策的有效性
                valid_agents = [
                    "INTENTION_AGENT", "COMPLETE_INTENT_NODE", "HISTORY_RAG_AGENT",
                    "TAG_CUSTOMER_AGENT", "COMPLETE_CUSTOMER_NODE", "PLAN_CREATOR_AGENT",
                    "COMPLETE_PLAN_NODE", "END"
                ]

                if nextAgent and nextAgent in valid_agents:
                    loguru_logger.info(f"✅ 路由决策: {nextAgent} (置信度: {routingConfidence:.2f})")
                    loguru_logger.debug(f"决策理由: {routingReasoning}")
                    return nextAgent

                # 如果没有有效的路由决策，默认进入意图识别
                loguru_logger.warning(f"❌ 路由决策无效: nextAgent={nextAgent}, 默认进入意图识别")
                return "INTENTION_AGENT"

            except Exception as e:
                loguru_logger.error(f"❌ 路由决策失败: {str(e)}")
                return "INTENTION_AGENT"

        workflow.add_conditional_edges(
            "ROUTER_AGENT",
            routerNextSelector,
            {
                "INTENTION_AGENT": "INTENTION_AGENT",
                "COMPLETE_INTENT_NODE": "COMPLETE_INTENT_NODE",
                "HISTORY_RAG_AGENT": "HISTORY_RAG_AGENT",
                "TAG_CUSTOMER_AGENT": "TAG_CUSTOMER_AGENT",
                "COMPLETE_CUSTOMER_NODE": "COMPLETE_CUSTOMER_NODE",
                "PLAN_CREATOR_AGENT": "PLAN_CREATOR_AGENT",
                "COMPLETE_PLAN_NODE": "COMPLETE_PLAN_NODE",
                "END": "END"
            }
        )



        # 历史数据检索Agent后的路由
        def historyRAGNextSelector(state):
            # 历史数据检索完成后，根据数据情况决定下一步
            if state.get("historicalCases"):
                # 有历史数据，检查是否需要客户标签
                if not state.get("customerTags") or not state["customerTags"].get("completed"):
                    return "TAG_CUSTOMER_AGENT"
                else:
                    # 直接进入方案生成
                    return "PLAN_CREATOR_AGENT"
            else:
                # 没有历史数据，回到路由重新决策
                return "ROUTER_AGENT"

        workflow.add_conditional_edges(
            "HISTORY_RAG_AGENT",
            historyRAGNextSelector,
            {
                "TAG_CUSTOMER_AGENT": "TAG_CUSTOMER_AGENT",
                "PLAN_CREATOR_AGENT": "PLAN_CREATOR_AGENT",
                "ROUTER_AGENT": "ROUTER_AGENT"
            }
        )
        # IntentionAgent后分流：如有缺失字段则进入补全节点，否则回路由
        def intentNextSelector(state: MarketingState) -> str:
            if state.get("needCompletion"):
                return "COMPLETE_INTENT_NODE"
            intentInfo = state.get("intentInfo", {}) or {}
            intentInfo["completed"] = True
            state.update({"intentInfo": intentInfo})
            return "ROUTER_AGENT"
        workflow.add_conditional_edges(
            "INTENTION_AGENT",
            intentNextSelector,
            {
                "COMPLETE_INTENT_NODE": "COMPLETE_INTENT_NODE",
                "ROUTER_AGENT": "ROUTER_AGENT"
            }
        )
        workflow.add_edge("COMPLETE_INTENT_NODE", "INTENTION_AGENT")  # 补全后回意图识别
        # TagCustomerAgent后分流：如无tags则进入补全节点，否则回路由
        def customerNextSelector(state):
            customerTags = state.get("customerTags", {})
            if not customerTags.get("tags"):
                return "COMPLETE_CUSTOMER_NODE"
            customerTags["completed"] = True
            state["customerTags"] = customerTags
            return "ROUTER_AGENT"
        workflow.add_conditional_edges(
            "TAG_CUSTOMER_AGENT",
            customerNextSelector,
            {
                "COMPLETE_CUSTOMER_NODE": "COMPLETE_CUSTOMER_NODE",
                "ROUTER_AGENT": "ROUTER_AGENT"
            }
        )
        workflow.add_edge("COMPLETE_CUSTOMER_NODE", "TAG_CUSTOMER_AGENT")  # 补全后回客群标签
        # PlanCreatorAgent后分流：如无content则进入补全节点，否则回路由
        def planNextSelector(state):
            planDraft = state.get("planDraft", {})
            if not planDraft.get("content"):
                return "COMPLETE_PLAN_NODE"

            # 🔥 方案生成完成，标记为已完成并准备展示给用户
            planDraft["completed"] = True
            planDraft["needsConfirmation"] = True
            planDraft["status"] = "awaiting_confirmation"
            state["planDraft"] = planDraft

            # 设置工作流状态为等待用户确认
            state["workflowStatus"] = "plan_generated"
            state["nextAction"] = "show_plan_to_user"

            return "END"
        workflow.add_conditional_edges(
            "PLAN_CREATOR_AGENT",
            planNextSelector,
            {
                "COMPLETE_PLAN_NODE": "COMPLETE_PLAN_NODE",
                "END": "END"  # 🔥 添加END选项
            }
        )
        workflow.add_edge("COMPLETE_PLAN_NODE", "PLAN_CREATOR_AGENT")  # 补全后回方案生成
        return workflow

    # --------- 补全节点方法集中放一起 ---------
    def completeIntentNode(self, state: MarketingState) -> Any:
        """意图补全节点 - 中断工作流等待用户输入"""
        # 构建中断消息
        guidancePrompts = state.get("guidancePrompts", {})
        fieldSuggestions = state.get("fieldSuggestions", {})
        followupQuestions = state.get("followupQuestions", {})
        missingFields = state.get("missingFields", {})

        message = {
            "type": "intent_completion",
            "guidancePrompts": guidancePrompts,
            "fieldSuggestions": fieldSuggestions,
            "followupQuestions": followupQuestions,
            "missingFields": missingFields.get("missingCoreFields", []) if missingFields else [],
            "stateContext": state
        }

        # 返回中断信号和消息
        return interrupt(message)

    def completeCustomerNode(self, state: MarketingState) -> Dict[str, Any]:
        """
        补全客群节点 - 使用标准化中断处理
        """
        customerTags = state.get("customerTags", {})

        if not customerTags.get("tags"):
            # 使用标准化的中断格式，提供更丰富的用户引导
            userInput = interrupt({
                "type": "user_input_needed",
                "nodeId": "COMPLETE_CUSTOMER_NODE",
                "prompt": "请补充目标客群信息，以便为您制定精准的营销方案",
                "description": "客群标签帮助我们了解您的目标用户特征，制定更有效的营销策略",
                "missingFields": [
                    {
                        "field": "tags",
                        "label": "客群标签",
                        "required": True,
                        "type": "multiselect",
                        "placeholder": "请选择或输入客群标签"
                    }
                ],
                "suggestions": [
                    {"value": "新注册用户", "description": "刚注册的新用户"},
                    {"value": "活跃用户", "description": "经常使用产品的用户"},
                    {"value": "高价值客户", "description": "消费金额较高的用户"},
                    {"value": "流失用户", "description": "长时间未使用的用户"},
                    {"value": "年轻用户", "description": "18-30岁年龄段用户"},
                    {"value": "企业客户", "description": "B2B企业用户"}
                ],
                "currentInfo": customerTags,
                "canSkip": False,
                "skipReason": "客群信息是制定营销方案的必要条件"
            })

            # 处理用户输入
            if isinstance(userInput, dict):
                if "tags" in userInput:
                    customerTags["tags"] = userInput["tags"]
                # 合并其他可能的客群信息
                customerTags.update({k: v for k, v in userInput.items() if k != "tags"})
            elif isinstance(userInput, str):
                # 如果用户直接输入文本，解析为标签
                customerTags["tags"] = [tag.strip() for tag in userInput.split(",")]
            elif isinstance(userInput, list):
                # 如果用户选择了多个选项
                customerTags["tags"] = userInput

        return {"customerTags": customerTags}

    def completePlanNode(self, state: MarketingState) -> Dict[str, Any]:
        """
        补全方案节点 - 使用标准化中断处理
        """
        planDraft = state.get("planDraft", {})

        if not planDraft.get("content"):
            # 使用标准化的中断格式
            userInput = interrupt({
                "type": "user_input_needed",
                "nodeId": "COMPLETE_PLAN_NODE",
                "prompt": "请补充营销方案的具体内容",
                "description": "详细的方案内容有助于制定更完整的营销策略",
                "missingFields": [
                    {
                        "field": "content",
                        "label": "方案内容",
                        "required": True,
                        "type": "textarea",
                        "placeholder": "请描述活动主题、执行步骤、预期效果等"
                    }
                ],
                "suggestions": [
                    {"value": "限时折扣活动", "description": "设置时间限制的优惠活动"},
                    {"value": "新用户专享", "description": "针对新用户的专属优惠"},
                    {"value": "会员积分兑换", "description": "使用积分兑换优惠或礼品"},
                    {"value": "社交分享奖励", "description": "分享获得额外优惠"}
                ],
                "currentInfo": planDraft,
                "canSkip": False,
                "skipReason": "方案内容是营销活动的核心要素"
            })

            # 处理用户输入
            if isinstance(userInput, dict):
                planDraft.update(userInput)
            elif isinstance(userInput, str):
                planDraft["content"] = userInput

        return {"planDraft": planDraft}

    async def executeStreamWorkflow(self, userInput: str, threadId: str = None, messageId: str = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        增强版流式执行营销工作流，支持多种流式模式提升用户体验

        Args:
            userInput: 用户输入
            threadId: 线程ID（仅内部使用，不返回前端）
            messageId: 消息ID（用于标识同一轮对话的所有事件）

        Yields:
            Dict[str, Any]: 统一结构的流式事件数据（content + data）
        """
        try:
            self.logger.info(f"开始流式执行营销工作流，用户输入: {userInput[:50]}...")

            # 生成或使用现有的ID
            if not threadId:
                threadId = str(uuid.uuid4())
            if not messageId:
                messageId = f"msg_{str(uuid.uuid4())[:8]}_{int(datetime.now().timestamp() * 1000)}"

            # 配置checkpointer
            config = {"configurable": {"thread_id": threadId}}

            # 准备初始状态
            try:
                existingState = self.app.get_state(config)
                if existingState and existingState.values:
                    # 继续现有会话
                    currentState = existingState.values
                    currentState["userInput"] = userInput
                else:
                    # 新会话，初始化状态
                    currentState = MarketingState(userInput=userInput, planRetryCount=0)
            except:
                currentState = MarketingState(userInput=userInput, planRetryCount=0)

            # 记录开始时间
            startTime = int(datetime.now().timestamp())

            # 设置当前消息ID，供回调函数使用
            self._current_message_id = messageId

            # 推送工作流开始事件
            yield StreamEventBuilder.createWorkflowStartedEvent(
                messageId=messageId,
                conversationId="",  # 将在上层添加
                totalSteps=5
            )

            # 初始化流式事件收集器和回调管理
            self._current_stream_events = []
            self.currentStreamEvents = []  # 用于节点开始/结束事件
            self._active_callbacks = {}  # 重置回调函数引用
            self._current_message_id = messageId  # 保存当前消息ID供节点包装器使用
            self.currentMessageId = messageId  # 用于节点事件

            # 使用LangGraph原生流式模式：messages + updates
            try:
                workflow_completed = False
                for streamEvent in self.app.stream(
                    currentState,
                    config=config,
                    stream_mode=["messages", "updates", "custom"]  # 添加custom模式
                ):
                    # 首先检查并发送节点开始事件
                    if hasattr(self, '_current_stream_events') and self._current_stream_events:
                        for event in self._current_stream_events:
                            if "conversationId" not in event or not event["conversationId"]:
                                event["conversationId"] = ""  # 将在上层添加
                            yield event
                        self._current_stream_events.clear()  # 清空已发送的事件

                    # 处理LangGraph原生流式事件
                    if isinstance(streamEvent, tuple):
                        mode, data = streamEvent

                        if mode == "messages":
                            # 处理LLM token流（LangGraph原生能力）
                            token_event = self._handleLLMToken(data, threadId, messageId)
                            if token_event:  # 只有非空内容才发送事件
                                yield token_event

                        elif mode == "updates":
                            # 处理节点完成事件（LangGraph原生能力）
                            self.logger.debug(f"处理节点更新事件: {data}")
                            for event in self._handleNodeUpdates(data, threadId, messageId):
                                yield event

                        elif mode == "custom":
                            # 处理自定义事件（包括业务数据和中断）
                            self.logger.debug(f"处理自定义事件: {data}")
                            customEvent = self.processStreamEvent(data, "custom", messageId, "")
                            if customEvent:
                                yield customEvent

                    else:
                        # 处理其他格式的事件（兼容性处理）
                        self.logger.debug(f"收到未知格式事件: {type(streamEvent)}")
                        # 尝试按原格式处理
                        if isinstance(streamEvent, dict):
                            for nodeName, nodeOutput in streamEvent.items():
                                yield {
                                    "event": "nodeFinished",
                                    "messageId": messageId,
                                    "conversationId": "",
                                    "content": f"{self.getNodeDisplayName(nodeName)}执行完成",
                                    "data": {
                                        "nodeId": nodeName,
                                        "nodeResult": nodeOutput
                                    },
                                    "createdAt": int(datetime.now().timestamp() * 1000)
                                }

                # 检查是否因中断而结束
                final_state = self.app.get_state(config)
                if final_state and final_state.next:
                    # 工作流被中断，还有下一个节点要执行
                    workflowInterruptedEvent = StreamEventBuilder.createWorkflowEndEvent(
                        messageId=messageId,
                        conversationId="",  # 将在上层添加
                        status=WorkflowStatus.INTERRUPTED,
                        totalSteps=5,
                        completedSteps=3,  # 估算已完成步数
                        elapsedTime=int(datetime.now().timestamp()) - startTime,
                        error="需要用户补充信息"
                    )
                    yield workflowInterruptedEvent
                else:
                    # 工作流正常完成 - 不在这里发送workflowFinished事件，由上层API路由统一发送
                    workflow_completed = True

            except Exception as stream_error:
                error_info = f"streamExecutionError: {type(stream_error).__name__} - {str(stream_error) or repr(stream_error)} - {traceback.format_exc().replace(chr(10), ' | ')}"
                self.logger.error(error_info)

                # 检查是否是中断导致的异常
                if self._is_interrupt_error(stream_error):
                    # 检查当前状态
                    try:
                        final_state = self.app.get_state(config)
                        if final_state and final_state.next:
                            yield StreamEventBuilder.createInterruptEvent(
                                messageId=messageId,
                                conversationId="",  # 将在上层添加
                                content="工作流已中断，等待用户补充信息",
                                data={
                                    "threadId": threadId,
                                    "nextNode": final_state.next[0] if final_state.next else None,
                                    "status": "interrupted",
                                    "statusText": "需要更多信息才能继续",
                                    "showContinue": True,
                                    "canResume": True
                                }
                            )
                        else:
                            yield StreamEventBuilder.createWorkflowEndEvent(
                                messageId=messageId,
                                conversationId="",  # 将在上层添加
                                status="succeeded",
                                totalSteps=5,
                                completedSteps=5,
                                elapsedTime=0.0
                            )
                    except Exception as state_error:
                        self.logger.error(f"获取状态失败: {str(state_error)}")
                        workflowInterruptedEvent = StreamEventBuilder.createWorkflowEndEvent(
                            messageId=messageId,
                            conversationId="",
                            status=WorkflowStatus.INTERRUPTED,
                            totalSteps=5,
                            completedSteps=2,
                            elapsedTime=int(datetime.now().timestamp()) - startTime,
                            error="获取状态失败"
                        )
                        yield workflowInterruptedEvent
                else:
                    streamErrorEvent = StreamEventBuilder.createErrorEvent(
                        messageId=messageId,
                        conversationId="",  # 将在上层添加
                        errorCode="STREAM_EXECUTION_ERROR",
                        errorMessage=f"流式执行错误: {str(stream_error)}"
                    )
                    yield streamErrorEvent

        except GeneratorExit:
            # 处理异步生成器被强制关闭的情况
            self.logger.info("流式工作流被客户端中断")
            # 不要在这里 yield，直接 return 或 raise
            return
        except Exception as e:
            # 检查是否是LangGraph的中断异常
            if self._is_interrupt_error(e):
                try:
                    final_state = self.app.get_state(config)
                    if final_state and final_state.next:
                        workflowInterruptedEvent = StreamEventBuilder.createWorkflowEndEvent(
                            messageId=messageId,
                            conversationId="",
                            status=WorkflowStatus.INTERRUPTED,
                            totalSteps=5,
                            completedSteps=3,
                            elapsedTime=int(datetime.now().timestamp()) - startTime,
                            error="工作流已中断，等待用户补充信息"
                        )
                        yield workflowInterruptedEvent
                    else:
                        # 工作流正常完成 - 不在这里发送事件，由上层统一处理
                        pass
                except Exception as state_error:
                    self.logger.error(f"获取中断状态失败: {str(state_error)}")
                    workflowInterruptedEvent = StreamEventBuilder.createWorkflowEndEvent(
                        messageId=messageId,
                        conversationId="",
                        status=WorkflowStatus.INTERRUPTED,
                        totalSteps=5,
                        completedSteps=2,
                        elapsedTime=int(datetime.now().timestamp()) - startTime,
                        error="获取中断状态失败"
                    )
                    yield workflowInterruptedEvent
            else:
                self.logger.error(f"流式工作流执行失败: {str(e)}")
                workflowErrorEvent = StreamEventBuilder.createErrorEvent(
                    messageId=messageId,
                    conversationId="",
                    errorCode="WORKFLOW_EXECUTION_ERROR",
                    errorMessage=f"工作流执行失败: {str(e)}"
                )
                yield workflowErrorEvent
        finally:
            # 清理回调函数引用，避免内存泄漏
            if hasattr(self, '_active_callbacks'):
                self._active_callbacks.clear()
            if hasattr(self, '_current_stream_events'):
                self._current_stream_events.clear()

    def _is_interrupt_error(self, error: Exception) -> bool:
        """检查是否是中断相关的错误"""
        error_str = str(error).lower()
        error_type = str(type(error).__name__).lower()

        # 检查多种中断相关的错误模式
        interrupt_patterns = [
            "interrupt",
            "interrupted",
            "graphinterrupt",
            "nodeinterrupt",
            "execution interrupted",
            "workflow interrupted"
        ]

        for pattern in interrupt_patterns:
            if pattern in error_str or pattern in error_type:
                return True

        return False

    async def isInterruptedWorkflow(self, threadId: str) -> bool:
        """检查工作流是否处于中断状态"""
        if not threadId:
            return False

        try:
            config = {"configurable": {"thread_id": threadId}}
            state = self.app.get_state(config)
            # 如果有next节点，说明工作流被中断了
            return state and hasattr(state, 'next') and state.next
        except Exception as e:
            self.logger.debug(f"检查中断状态失败: {e}")
            return False

    async def resumeInterruptedWorkflow(self, userInput: str, threadId: str, messageId: str) -> AsyncGenerator[Dict[str, Any], None]:
        """恢复被中断的工作流"""
        try:
            from langgraph.types import Command

            config = {"configurable": {"thread_id": threadId}}
            self.logger.info(f"恢复中断的工作流: {threadId}")

            # 使用Command原语恢复工作流
            async for streamEvent in self.app.astream(
                Command(resume=userInput),
                config=config
            ):
                if isinstance(streamEvent, tuple):
                    mode, data = streamEvent

                    if mode == "messages":
                        # 处理LLM token流
                        event = self.processStreamEvent(data, "message", messageId, "")
                        if event:
                            yield event

                    elif mode == "updates":
                        # 处理节点更新
                        events = self.handleNodeUpdateEvents(data, messageId, "")
                        for event in events:
                            yield event

                    elif mode == "custom":
                        # 处理自定义事件（包括新的中断）
                        event = self.processStreamEvent(data, "custom", messageId, "")
                        if event:
                            yield event

            # 发送工作流完成事件
            yield StreamEventBuilder.createWorkflowEndEvent(
                messageId=messageId,
                conversationId="",
                status="succeeded",
                totalSteps=5,
                completedSteps=5,
                elapsedTime=0.0
            )

        except Exception as e:
            self.logger.error(f"恢复工作流失败: {str(e)}")
            yield {
                "event": "error",
                "messageId": messageId,
                "conversationId": "",
                "content": f"恢复工作流失败: {str(e)}",
                "data": {"errorCode": "RESUME_FAILED"},
                "createdAt": int(datetime.now().timestamp() * 1000)
            }

    def processStreamEvent(self, eventData: Any, eventType: str, messageId: str, conversationId: str = "") -> Optional[Dict[str, Any]]:
        """
        统一的流式事件处理器

        Args:
            eventData: 事件数据
            eventType: 事件类型 (message, node_update, custom)
            messageId: 消息ID
            conversationId: 对话ID

        Returns:
            Optional[Dict[str, Any]]: 处理后的事件数据
        """
        if eventType == "message":
            return self.handleMessageEvent(eventData, messageId, conversationId)
        elif eventType == "node_update":
            return self.handleNodeUpdateEvent(eventData, messageId, conversationId)
        elif eventType == "custom":
            return self.handleCustomEvent(eventData, messageId, conversationId)
        return None

    def handleMessageEvent(self, messageData: Any, messageId: str, conversationId: str) -> Optional[Dict[str, Any]]:
        """处理LLM消息事件"""
        content = ""

        if isinstance(messageData, tuple) and len(messageData) >= 1:
            messageChunk = messageData[0]
            if hasattr(messageChunk, 'content'):
                content = str(messageChunk.content)

        # 使用统一的内容过滤
        if not content or not shouldShowToUser(content):
            return None

        return StreamEventBuilder.createMessageEvent(
            messageId=messageId,
            conversationId=conversationId,
            content=content
        )

    def handleNodeUpdateEvent(self, updateData: Dict[str, Any], messageId: str, conversationId: str) -> Optional[Dict[str, Any]]:
        """处理单个节点更新事件"""
        for nodeName, nodeOutput in updateData.items():
            # 过滤END节点
            if nodeName == "END":
                continue

            return StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId=conversationId,
                nodeId=nodeName,
                status="succeeded",
                stepNumber=getNodeIndex(nodeName),
                outputs=nodeOutput
            )
        return None

    def handleNodeUpdateEvents(self, updateData: Dict[str, Any], messageId: str, conversationId: str):
        """处理多个节点更新事件（生成器）"""
        for nodeName, nodeOutput in updateData.items():
            # 过滤END节点
            if nodeName == "END":
                continue

            yield StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId=conversationId,
                nodeId=nodeName,
                status="succeeded",
                stepNumber=getNodeIndex(nodeName),
                outputs=nodeOutput
            )

    def handleCustomEvent(self, customData: Any, messageId: str, conversationId: str) -> Optional[Dict[str, Any]]:
        """处理自定义事件 - 支持业务流式输出"""
        if isinstance(customData, dict):
            event_name = customData.get("event", "custom")

            # 处理业务内容流式事件
            if event_name == "businessContentChunk":
                return StreamEventBuilder.createMessageEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    content=customData.get("content", ""),
                    contentType=customData.get("contentType"),
                    nodeId=customData.get("nodeId"),
                    runId=customData.get("runId")  # 支持custom事件的runId
                )

            elif event_name == "businessContentComplete":
                return StreamEventBuilder.createMessageEndEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    contentType=customData.get("contentType"),
                    nodeId=customData.get("nodeId"),
                    fullContent=customData.get("fullContent"),
                    runId=customData.get("runId")  # 支持custom事件的runId
                )

            # 处理思考过程事件
            elif event_name == "thinkingProcess":
                return StreamEventBuilder.createMessageEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    content=customData.get("content", ""),
                    contentType="thinkingProcess",
                    nodeId=customData.get("nodeId")
                )

            # 处理其他自定义事件
            else:
                eventType = customData.get("type", "custom")
                content = customData.get("content", customData.get("message", "自定义事件"))

                return {
                    "event": eventType,
                    "messageId": messageId,
                    "conversationId": conversationId,
                    "content": content,
                    "data": customData.get("data", customData),
                    "createdAt": int(datetime.now().timestamp() * 1000)
                }
        return None



    def createNodeWrapper(self, nodeId: str, originalFunc, enableStreaming: bool = True, sendFinishEvent: bool = False):
        """
        统一的节点包装器，支持流式和非流式模式

        Args:
            nodeId: 节点ID
            originalFunc: 原始节点执行函数
            enableStreaming: 是否启用流式处理
            sendFinishEvent: 是否发送完成事件

        Returns:
            包装后的节点函数
        """
        def wrappedNodeFunc(state):
            # 🔥 方案3: 维护当前执行节点状态
            self._current_executing_node = nodeId
            self.logger.debug(f"🔥 节点开始执行: {nodeId}")
            
            # 发送节点开始事件
            if hasattr(self, '_current_stream_events') and self._current_stream_events is not None:
                self.sendNodeStartEvent(nodeId, state)

            try:
                # 执行原始节点函数
                result = originalFunc(state)
                
                # 发送节点完成事件（如果需要）
                if sendFinishEvent and hasattr(self, '_current_stream_events') and self._current_stream_events is not None:
                    self.sendNodeFinishEvent(nodeId, result)
                
                return result
            finally:
                # 确保节点执行完成后清理状态
                self._current_executing_node = None
                self.logger.debug(f"🔥 节点执行完成: {nodeId}")

        return wrappedNodeFunc

    def sendNodeStartEvent(self, nodeId: str, state: Dict[str, Any]):
        """发送节点开始事件"""
        try:
            messageId = getattr(self, 'currentMessageId', f"msg_{str(uuid.uuid4())[:8]}")

            nodeStartedEvent = StreamEventBuilder.createNodeStartedEvent(
                messageId=messageId,
                conversationId="",  # 将在上层添加
                nodeId=nodeId,
                stepNumber=getNodeIndex(nodeId),
                totalSteps=5
            )

            self._current_stream_events.append(nodeStartedEvent)
            self.logger.debug(f"发送节点开始事件: {nodeId}")
        except Exception as e:
            self.logger.warning(f"发送节点开始事件失败: {e}")

    def sendNodeFinishEvent(self, nodeId: str, result: Dict[str, Any]):
        """发送节点完成事件"""
        try:
            messageId = getattr(self, 'currentMessageId', f"msg_{str(uuid.uuid4())[:8]}")

            nodeFinishedEvent = StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId="",
                nodeId=nodeId,
                nodeName=getNodeDisplayName(nodeId),
                status="succeeded",
                stepNumber=getNodeIndex(nodeId),
                outputs=result
            )

            self._current_stream_events.append(nodeFinishedEvent)
            self.logger.debug(f"发送节点完成事件: {nodeId}")
        except Exception as e:
            self.logger.warning(f"发送节点完成事件失败: {e}")


    def _handleLLMToken(self, messageData, threadId: str, messageId: str) -> Dict[str, Any]:
        """
        处理LLM生成的token，实现打字机效果

        Args:
            messageData: LLM消息数据 (通常是tuple: (AIMessageChunk, metadata))
            threadId: 线程ID
            messageId: 消息ID

        Returns:
            Dict[str, Any]: LLM token事件
        """
        # 提取token内容和runId
        content = ""
        run_id = ""

        # LangGraph传递的是tuple: (AIMessageChunk, metadata)
        if isinstance(messageData, tuple) and len(messageData) >= 1:
            message_chunk = messageData[0]
            if hasattr(message_chunk, 'content'):
                content = str(message_chunk.content)
            if hasattr(message_chunk, 'id'):
                run_id = str(message_chunk.id)
        # 处理其他类型的消息数据
        elif hasattr(messageData, 'content'):
            content = str(messageData.content)
            if hasattr(messageData, 'id'):
                run_id = str(messageData.id)
        elif isinstance(messageData, dict):
            content = messageData.get('content', '')
            run_id = messageData.get('id', '')
        elif isinstance(messageData, str):
            content = messageData
        else:
            # 尝试转换为字符串
            content = str(messageData)

        # 检测新的LLM调用，重置thinking状态
        if not hasattr(self, '_last_run_id'):
            self._last_run_id = None

        if run_id and run_id != self._last_run_id:
            self.contentFilter.resetForNewCall()
            self._last_run_id = run_id

        # 过滤空内容和结束标记
        if not content or content.strip() == "":
            return None

        # 过滤工具调用相关的技术内容
        if self._isToolCallContent(messageData):
            self.logger.debug(f"过滤工具调用内容: runId={run_id}")
            return None

        # 获取当前执行的节点
        current_node = self._getCurrentExecutingNode()

        # 严格过滤：提取标签内容并获取标签类型
        filtered_content, content_type = self.contentFilter.filterContent(content)
        if not filtered_content:
            return None
        content = filtered_content

        # 调试信息：检查runId是否正确提取
        if not run_id:
            self.logger.debug(f"⚠️ runId为空，messageData类型: {type(messageData)}")
            if isinstance(messageData, tuple) and len(messageData) >= 1:
                chunk = messageData[0]
                self.logger.debug(f"   chunk类型: {type(chunk)}, 有id属性: {hasattr(chunk, 'id')}")
                if hasattr(chunk, 'id'):
                    self.logger.debug(f"   chunk.id值: {chunk.id}")

        # 使用增强的消息事件构建器
        return StreamEventBuilder.createMessageEvent(
            messageId=messageId,
            conversationId="",  # 将在上层添加
            content=content,
            contentType=content_type,  # 🔑 关键标识
            nodeId=current_node,
            runId=run_id if run_id else None  # 🔑 确保空字符串不传递
        )

    def _getCurrentExecutingNode(self) -> str:
        """
        获取当前正在执行的节点ID - 优化版本

        Returns:
            str: 当前节点ID，如果无法确定则返回默认值
        """
        # 🔥 方案1: 优先使用实例变量跟踪（最准确）
        if hasattr(self, '_current_executing_node') and self._current_executing_node:
            return self._current_executing_node

        # 方案2: 通过线程本地存储（如果Agent设置了）
        import threading
        current_thread = threading.current_thread()
        if hasattr(current_thread, 'current_node'):
            return current_thread.current_node

        # 方案3: 通过调用栈分析（备用方案）
        import inspect

        for frame_info in inspect.stack():
            frame_name = frame_info.function.lower()
            filename = frame_info.filename.lower()

            # 更精确的节点识别
            if 'routeragent' in filename or 'router' in frame_name:
                return "ROUTER_AGENT"
            elif 'intentionagent' in filename or 'intention' in frame_name:
                return "INTENTION_AGENT"
            elif 'tagcustomeragent' in filename or ('customer' in frame_name and 'tag' in frame_name):
                return "TAG_CUSTOMER_AGENT"
            elif 'plancreatoragent' in filename or ('plan' in frame_name and 'creator' in frame_name):
                return "PLAN_CREATOR_AGENT"
            elif 'historyragagent' in filename or 'history' in frame_name:
                return "HISTORY_RAG_AGENT"

        # 默认返回通用节点
        return "UNKNOWN_AGENT"



    def _isToolCallContent(self, messageData) -> bool:
        """
        检测是否是工具调用相关的内容

        Args:
            messageData: 消息数据

        Returns:
            bool: 是否是工具调用内容
        """
        # 检查是否是tuple格式的消息
        if isinstance(messageData, tuple) and len(messageData) >= 1:
            message_chunk = messageData[0]

            # 检查是否包含工具调用
            if hasattr(message_chunk, 'additional_kwargs') and message_chunk.additional_kwargs:
                tool_calls = message_chunk.additional_kwargs.get('tool_calls', [])
                if tool_calls:
                    return True

            # 检查内容是否为空但有其他属性（通常是工具调用的标志）
            if hasattr(message_chunk, 'content'):
                content = str(message_chunk.content).strip()
                if not content and hasattr(message_chunk, 'additional_kwargs'):
                    return True

        return False


    def _handleNodeUpdates(self, updateData: Dict[str, Any], threadId: str, messageId: str) -> Generator[Dict[str, Any], None, None]:
        """
        处理节点更新事件，提供丰富的业务信息

        Args:
            updateData: 节点更新数据
            threadId: 线程ID（仅内部使用）
            messageId: 消息ID

        Yields:
            Dict[str, Any]: 统一结构的节点事件（content + data）
        """
        for nodeName, nodeOutput in updateData.items():
            # 过滤END节点，它不是实际的业务节点
            if nodeName == "END":
                self.logger.debug(f"跳过END节点的事件发送: {nodeName}")
                continue

            # 处理不同类型的nodeOutput
            if isinstance(nodeOutput, tuple):
                # 如果是tuple，可能是(state, metadata)格式，取第一个元素
                actual_output = nodeOutput[0] if nodeOutput else {}
            elif isinstance(nodeOutput, dict):
                actual_output = nodeOutput
            else:
                # 其他类型，包装成字典
                actual_output = {"result": nodeOutput}

            # 发送节点完成事件，使用用户友好的消息
            friendlyCompleteMessage = generateFriendlyMessage(nodeName, 'complete')

            nodeFinishedEvent = StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId="",  # 将在上层添加
                nodeId=nodeName,
                status=NodeStatus.SUCCEEDED,
                stepNumber=getNodeIndex(nodeName),
                elapsedTime=0.0,  # 可以计算实际执行时间
                outputs=actual_output
            )

            # 覆盖默认的content为用户友好消息
            nodeFinishedEvent["content"] = friendlyCompleteMessage
            yield nodeFinishedEvent

            # 3. 如果需要用户输入，发送引导事件
            if isinstance(actual_output, dict) and actual_output.get("needCompletion", False):
                userGuidanceEvent = self._generateUserGuidanceEvent(
                    nodeName, actual_output, messageId
                )
                if userGuidanceEvent:
                    yield userGuidanceEvent





    def _generateUserGuidanceEvent(
        self,
        nodeName: str,
        nodeOutput: Dict[str, Any],
        messageId: str
    ) -> Optional[Dict[str, Any]]:
        """
        生成用户引导事件

        Args:
            nodeName: 节点名称
            nodeOutput: 节点输出
            messageId: 消息ID

        Returns:
            Optional[Dict[str, Any]]: 用户引导事件
        """
        missingFields = nodeOutput.get("missingFields", [])
        if not missingFields:
            return None

        # 根据缺失字段生成引导消息和交互数据
        guidanceMessage = "为了制定更精准的方案，还需要了解一些信息："
        interactionData = {
            "suggestedResponses": [],
            "formFields": []
        }

        # 根据缺失字段类型生成不同的交互方式
        if "budget" in missingFields:
            interactionData["suggestedResponses"].extend([
                "预算1万以下", "预算1-5万", "预算5万以上"
            ])

        if "timeline" in missingFields:
            interactionData["suggestedResponses"].extend([
                "时间1个月内", "时间1-3个月", "时间3个月以上"
            ])

        return StreamEventBuilder.createUserGuidanceEvent(
            messageId=messageId,
            conversationId="",  # 将在上层添加
            guidanceType="conversational",  # 默认对话式
            guidanceMessage=guidanceMessage,
            missingFields=missingFields,
            interactionData=interactionData
        )



    def _generateGuidanceEvent(self, nodeName: str, nodeOutput: Dict[str, Any], threadId: str) -> Dict[str, Any]:
        """
        生成用户引导事件

        Args:
            nodeName: 节点名称
            nodeOutput: 节点输出
            threadId: 线程ID

        Returns:
            Dict[str, Any]: 引导事件
        """
        if not nodeOutput.get("needCompletion", False):
            return None

        # 提取引导信息
        missingFields = nodeOutput.get("missingFields", {})
        guidancePrompts = nodeOutput.get("guidancePrompts", {})
        fieldSuggestions = nodeOutput.get("fieldSuggestions", {})

        # 构建引导事件
        return {
            "eventType": "user_guidance",
            "nodeName": nodeName,
            "message": "需要补充更多信息",
            "threadId": threadId,
            "timestamp": datetime.now().isoformat(),
            "guidance": {
                "prompt": guidancePrompts.get("comprehensive", "请补充以下信息:"),
                "missingFields": missingFields.get("missingCoreFields", []),
                "suggestions": fieldSuggestions,
                "priority": missingFields.get("priority", {})
            }
        }

    def getMermaidDiagram(self) -> str:
        """
        获取工作流的Mermaid图

        Returns:
            str: Mermaid图代码
        """
        try:
            return self.workflow.compile().get_graph().draw_mermaid()
        except Exception as e:
            self.logger.error(f"生成Mermaid图失败: {str(e)}")
            return "graph TD\n    A[工作流图生成失败]"


def createWorkflow() -> MarketingWorkflow:
    """
    创建工作流实例

    Returns:
        MarketingWorkflow: 工作流实例
    """
    return MarketingWorkflow()
