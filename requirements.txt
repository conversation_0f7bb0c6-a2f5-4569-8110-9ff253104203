# Core FastAPI and web framework dependencies
fastapi==0.115.12
uvicorn==0.34.2
starlette==0.46.2
pydantic==2.10.6
pydantic-settings==2.8.1

# AI and LLM dependencies
langchain==0.3.21
langchain-core==0.3.48
langchain-openai==0.3.6
langchain-community==0.3.19
langchain-chroma==0.2.2
langchain-text-splitters==0.3.7
langgraph==0.2.74
langgraph-checkpoint>=2.0.26
# langgraph-checkpoint-postgres==2.0.21  # 不需要PostgreSQL支持
langgraph-prebuilt==0.1.7
openai==1.92.2
anthropic==0.55.0

# Database and storage (SQLite only, no PostgreSQL)
SQLAlchemy==2.0.38
chromadb==0.6.3
qdrant-client==1.13.3

# Redis support for LangGraph checkpointer
redis==5.2.1
langgraph-checkpoint-redis==0.0.8

# Logging and monitoring
loguru

# Environment and configuration
python-dotenv==1.1.1
python-multipart==0.0.20

# HTTP and networking
httpx==0.28.1
aiohttp==3.11.13
requests==2.32.3
aiofiles==24.1.0

# Data processing
pandas==2.2.3
numpy>=1.26.2,<2.0.0  # 兼容langchain-chroma的版本要求
pydantic-core==2.27.2

# Utilities
typer==0.15.2
click==8.2.1
python-dateutil==2.9.0.post0
PyYAML==6.0.2
