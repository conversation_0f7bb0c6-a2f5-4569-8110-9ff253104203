#!/usr/bin/env python3
"""
LLM连接测试脚本 - 验证LLM是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.llm import LlmFactory


def test_llm_connection():
    """测试LLM连接"""
    print("🧪 测试LLM连接")
    print("=" * 50)
    
    try:
        # 创建LLM实例
        llm = LlmFactory.getDefaultLLM()
        print("✅ LLM实例创建成功")
        
        # 测试简单查询
        test_prompt = "请回答：1+1等于几？"
        print(f"📝 测试提示词: {test_prompt}")
        
        response = llm.invoke(test_prompt)
        response_text = response.content if hasattr(response, 'content') else str(response)
        
        print(f"📤 LLM响应: {response_text}")
        print(f"📏 响应长度: {len(response_text)}")
        
        if response_text and response_text.strip():
            print("✅ LLM响应正常")
        else:
            print("❌ LLM返回空响应")
            
    except Exception as e:
        print(f"❌ LLM连接失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_json_generation():
    """测试JSON生成能力"""
    print("\n🧪 测试JSON生成能力")
    print("=" * 50)
    
    try:
        llm = LlmFactory.getDefaultLLM()
        
        # 测试JSON生成
        json_prompt = """
请生成一个简单的JSON响应，格式如下：
{
  "name": "测试",
  "value": 123,
  "items": ["a", "b", "c"]
}
"""
        print(f"📝 JSON测试提示词: {json_prompt}")
        
        response = llm.invoke(json_prompt)
        response_text = response.content if hasattr(response, 'content') else str(response)
        
        print(f"📤 LLM响应: {response_text}")
        
        # 尝试解析JSON
        import json
        try:
            parsed_json = json.loads(response_text)
            print("✅ JSON解析成功")
            print(f"📋 解析结果: {parsed_json}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
            print(f"🔍 原始响应: {response_text}")
            
    except Exception as e:
        print(f"❌ JSON生成测试失败: {str(e)}")

def test_json_cleaning():
    """测试JSON清理功能"""
    print("\n🧪 测试JSON清理功能")
    print("=" * 50)
    
    # 模拟LLM返回的带Markdown代码块的JSON
    test_responses = [
        # 正常的JSON
        '{"name": "test", "value": 123}',
        # 带```json标记的JSON
        '```json\n{"name": "test", "value": 123}\n```',
        # 带```标记的JSON
        '```\n{"name": "test", "value": 123}\n```',
        # 带多余空格的JSON
        '  {"name": "test", "value": 123}  ',
    ]
    
    import json
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n📝 测试响应 {i}: {repr(response)}")
        
        try:
            # 清理响应文本，移除可能的Markdown代码块标记
            cleaned_response = response.strip()
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]  # 移除 ```json
            if cleaned_response.startswith("```"):
                cleaned_response = cleaned_response[3:]  # 移除 ```
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]  # 移除结尾的 ```
            
            cleaned_response = cleaned_response.strip()
            
            print(f"🧹 清理后: {repr(cleaned_response)}")
            
            # 尝试解析JSON
            parsed_json = json.loads(cleaned_response)
            print(f"✅ JSON解析成功: {parsed_json}")
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")

def test_comprehensive_prompt():
    """测试全面引导话术生成"""
    print("\n🧪 测试全面引导话术生成")
    print("=" * 50)
    
    try:
        llm = LlmFactory.getDefaultLLM()
        
        # 模拟全面引导话术的提示词
        comprehensive_prompt = """
你是一名专业的营销顾问，需要为用户生成一个全面的引导话术，帮助他们完善营销活动信息。

【用户原始输入】
我想做一个营销活动

【当前已提取的信息】
- 活动目标: 未提供
- 目标客群: 未提供
- 预算: 未提供

【缺失字段统计】
- 核心字段缺失: 3 个 (goal, audience, budget)
- 次要字段缺失: 0 个
- 总计缺失: 3 个字段

【任务要求】
请生成一个全面的、通用的引导话术，要求：
1. 语气友好自然
2. 不要为每个字段单独生成话术，而是生成一个整体的引导话术
3. 引导用户一次性补充所有缺失的信息
4. 提供具体的示例和建议

【输出格式】
请严格按照JSON格式输出：
{
  "guidancePrompts": {
    "comprehensive": "全面的引导话术，引导用户补充所有缺失信息"
  },
  "fieldSuggestions": {
    "goal": ["建议值1", "建议值2", "建议值3"],
    "audience": ["建议值1", "建议值2", "建议值3"],
    "budget": ["建议值1", "建议值2", "建议值3"]
  },
  "followupQuestions": {
    "comprehensive": "整体的追问问题，引导用户提供完整信息"
  }
}
"""
        print(f"📝 全面引导话术测试提示词长度: {len(comprehensive_prompt)}")
        
        response = llm.invoke(comprehensive_prompt)
        response_text = response.content if hasattr(response, 'content') else str(response)
        
        print(f"📤 LLM响应长度: {len(response_text)}")
        print(f"📤 LLM响应前200字符: {response_text[:200]}...")
        
        # 清理响应文本，移除可能的Markdown代码块标记
        cleaned_response = response_text.strip()
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[7:]  # 移除 ```json
        if cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[3:]  # 移除 ```
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]  # 移除结尾的 ```
        
        cleaned_response = cleaned_response.strip()
        
        print(f"🧹 清理后响应前200字符: {cleaned_response[:200]}...")
        
        # 尝试解析JSON
        import json
        try:
            parsed_json = json.loads(cleaned_response)
            print("✅ 全面引导话术JSON解析成功")
            print(f"📋 guidancePrompts: {parsed_json.get('guidancePrompts', {})}")
            print(f"📋 fieldSuggestions: {parsed_json.get('fieldSuggestions', {})}")
            print(f"📋 followupQuestions: {parsed_json.get('followupQuestions', {})}")
        except json.JSONDecodeError as e:
            print(f"❌ 全面引导话术JSON解析失败: {str(e)}")
            print(f"🔍 清理后响应: {cleaned_response}")
            
    except Exception as e:
        print(f"❌ 全面引导话术测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🧪 LLM连接和功能测试")
    print("=" * 60)
    
    # 基本连接测试
    test_llm_connection()
    
    # JSON生成测试
    test_json_generation()
    
    # JSON清理功能测试
    test_json_cleaning()
    
    # 全面引导话术测试
    test_comprehensive_prompt()
    
    print("\n🎉 LLM测试完成！")

if __name__ == "__main__":
    main()
