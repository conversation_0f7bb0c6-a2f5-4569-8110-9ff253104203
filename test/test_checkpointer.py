#!/usr/bin/env python3
"""
测试Checkpointer集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.checkpointer import CheckpointerFactory, getDefaultSaver
from app.workflows.MarketingWorkflow import createWorkflow
import json

def test_checkpointer():
    """测试Checkpointer功能"""
    print("🧪 开始测试Checkpointer集成...")
    
    # 1. 测试创建Saver
    print("\n1. 测试创建Saver...")
    try:
        saver = getDefaultSaver()
        print("✅ 成功创建默认Saver")
        
        # 测试工厂方法
        factory_saver = CheckpointerFactory.create_saver("memory")
        print("✅ 成功通过工厂创建Saver")
    except Exception as e:
        print(f"❌ 创建Saver失败: {e}")
        return False
    
    # 2. 测试基本操作
    print("\n2. 测试基本操作...")
    workflow_id = "test_workflow_001"
    test_state = {
        "workflowId": workflow_id,
        "status": "running",
        "userInput": "测试营销需求",
        "currentStep": "intention",
        "progress": 0.25
    }
    
    try:
        # 保存状态
        success = saver.save(workflow_id, test_state)
        print(f"✅ 保存状态: {success}")
        
        # 检查是否存在
        exists = saver.exists(workflow_id)
        print(f"✅ 检查存在: {exists}")
        
        # 加载状态
        loaded_state = saver.load(workflow_id)
        print(f"✅ 加载状态: {loaded_state is not None}")
        
        # 验证数据一致性
        if loaded_state and loaded_state.get("userInput") == test_state["userInput"]:
            print("✅ 数据一致性验证通过")
        else:
            print("❌ 数据一致性验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 基本操作测试失败: {e}")
        return False
    
    # 3. 测试工作流集成
    print("\n3. 测试工作流集成...")
    try:
        workflow = createWorkflow()
        print("✅ 成功创建营销工作流")
        
        # 检查工作流是否使用了新的Checkpointer
        if hasattr(workflow, 'checkpointer'):
            print("✅ 工作流已集成Checkpointer")
        else:
            print("❌ 工作流未集成Checkpointer")
            return False
            
    except Exception as e:
        print(f"❌ 工作流集成测试失败: {e}")
        return False
    
    # 4. 测试统计信息
    print("\n4. 测试统计信息...")
    try:
        stats = saver.get_stats()
        print(f"✅ 获取统计信息: {stats}")
        
        workflows = saver.list_workflows()
        print(f"✅ 列出工作流: {workflows}")
        
    except Exception as e:
        print(f"❌ 统计信息测试失败: {e}")
        return False
    
    # 5. 清理测试数据
    print("\n5. 清理测试数据...")
    try:
        success = saver.delete(workflow_id)
        print(f"✅ 删除测试数据: {success}")
        
        exists = saver.exists(workflow_id)
        print(f"✅ 确认删除: {not exists}")
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return False
    
    print("\n🎉 Checkpointer集成测试完成！")
    return True

def test_api_integration():
    """测试API集成"""
    print("\n🧪 测试API集成...")
    
    try:
        from app.api.routes import checkpointer
        print("✅ 成功导入API中的Checkpointer")
        
        # 测试API中的Checkpointer操作
        test_id = "api_test_001"
        test_data = {"test": "data", "api": "integration"}
        
        checkpointer.save(test_id, test_data)
        loaded = checkpointer.load(test_id)
        
        if loaded and loaded.get("test") == "data":
            print("✅ API Checkpointer操作正常")
        else:
            print("❌ API Checkpointer操作异常")
            return False
            
        checkpointer.delete(test_id)
        
    except Exception as e:
        print(f"❌ API集成测试失败: {e}")
        return False
    
    print("🎉 API集成测试完成！")
    return True

if __name__ == "__main__":
    print("🚀 开始Checkpointer集成测试...")
    
    success1 = test_checkpointer()
    success2 = test_api_integration()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！Checkpointer集成成功！")
    else:
        print("\n❌ 部分测试失败，请检查集成")
        sys.exit(1)
