"""
测试路由Agent的LLM增强功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.RouterAgent import RouterAgent
from app.utils.models import MarketingState

def test_enhanced_router_agent():
    """测试路由Agent"""

    print("=== 测试路由Agent（LLM增强版） ===")
    
    # 初始化Agent
    agent = RouterAgent(maxRetries=3, llmConfidenceThreshold=0.6)
    print(f"✅ Agent初始化成功")
    print(f"   - 工具数量: {len(agent.tools)}")
    print(f"   - 置信度阈值: {agent.llmConfidenceThreshold}")
    
    # 测试用例1：方案生成意图
    print("\n--- 测试用例1：方案生成意图 ---")
    state1 = MarketingState(
        userInput="帮我生成一个拉新营销方案",
        planRetryCount=0
    )
    
    try:
        result1 = agent.execute(state1)
        print(f"✅ 执行成功")
        print(f"   - 下一步: {result1.get('nextStep', 'unknown')}")
        print(f"   - 当前步骤: {result1.get('currentStep', 'unknown')}")
        print(f"   - LLM增强: {result1.get('llmEnhanced', False)}")
        
        if result1.get('routingDecision'):
            decision = result1['routingDecision']
            print(f"   - 意图类型: {decision.get('intentType', 'unknown')}")
            print(f"   - 路由策略: {decision.get('routingStrategy', 'unknown')}")
            print(f"   - 置信度: {decision.get('confidence', 0):.2f}")
            print(f"   - 推理过程: {decision.get('reasoning', 'unknown')}")
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
    
    # 测试用例2：咨询意图
    print("\n--- 测试用例2：咨询意图 ---")
    state2 = MarketingState(
        userInput="我想了解一下营销活动怎么做",
        planRetryCount=0
    )
    
    try:
        result2 = agent.execute(state2)
        print(f"✅ 执行成功")
        print(f"   - 下一步: {result2.get('nextStep', 'unknown')}")
        print(f"   - 当前步骤: {result2.get('currentStep', 'unknown')}")
        print(f"   - LLM增强: {result2.get('llmEnhanced', False)}")
        
        if result2.get('routingDecision'):
            decision = result2['routingDecision']
            print(f"   - 意图类型: {decision.get('intentType', 'unknown')}")
            print(f"   - 路由策略: {decision.get('routingStrategy', 'unknown')}")
            print(f"   - 置信度: {decision.get('confidence', 0):.2f}")
            print(f"   - 推理过程: {decision.get('reasoning', 'unknown')}")
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
    
    # 测试用例3：不明确意图
    print("\n--- 测试用例3：不明确意图 ---")
    state3 = MarketingState(
        userInput="你好",
        planRetryCount=0
    )
    
    try:
        result3 = agent.execute(state3)
        print(f"✅ 执行成功")
        print(f"   - 下一步: {result3.get('nextStep', 'unknown')}")
        print(f"   - 当前步骤: {result3.get('currentStep', 'unknown')}")
        print(f"   - LLM增强: {result3.get('llmEnhanced', False)}")
        
        if result3.get('routingDecision'):
            decision = result3['routingDecision']
            print(f"   - 意图类型: {decision.get('intentType', 'unknown')}")
            print(f"   - 路由策略: {decision.get('routingStrategy', 'unknown')}")
            print(f"   - 置信度: {decision.get('confidence', 0):.2f}")
            print(f"   - 推理过程: {decision.get('reasoning', 'unknown')}")
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
    
    print("\n=== 测试完成 ===")

def test_tools_individually():
    """单独测试工具功能"""
    
    print("\n=== 测试工具功能 ===")
    
    agent = RouterAgent()
    
    # 测试意图分析工具
    print("\n--- 测试意图分析工具 ---")
    try:
        intent_tool = agent.tools[0]  # intentAnalysisTool
        result = intent_tool.invoke({"user_input": "帮我生成一个营销方案"})
        print(f"✅ 意图分析工具测试成功")
        print(f"   - 结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 意图分析工具测试失败: {str(e)}")
    
    # 测试状态分析工具
    print("\n--- 测试状态分析工具 ---")
    try:
        state_tool = agent.tools[1]  # stateAnalysisTool
        test_state = {
            "userInput": "test",
            "intentInfo": {"goal": "拉新"},
            "customerTags": None
        }
        result = state_tool.invoke({"state_data": str(test_state)})
        print(f"✅ 状态分析工具测试成功")
        print(f"   - 结果: {result[:100]}...")
    except Exception as e:
        print(f"❌ 状态分析工具测试失败: {str(e)}")
    
    print("\n=== 工具测试完成 ===")

if __name__ == "__main__":
    # 测试增强版路由Agent
    test_enhanced_router_agent()
    
    # 测试工具功能
    test_tools_individually()
