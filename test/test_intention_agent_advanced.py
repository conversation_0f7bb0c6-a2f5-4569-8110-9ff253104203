#!/usr/bin/env python3
"""
测试IntentionAgent的智能JSON提取功能
"""

import json
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.IntentionAgent import IntentionAgent
from app.llm.LlmFactory import getDefaultLLM
from app.core.logging_config import getLogger

logger = getLogger(__name__)

def test_json_extraction():
    """测试智能JSON提取功能"""
    print("=== 测试智能JSON提取功能 ===")
    
    # 创建IntentionAgent实例
    llm = getDefaultLLM()
    agent = IntentionAgent(llm)
    
    # 测试用例：不同的LLM响应格式
    test_cases = [
        {
            "name": "标准JSON格式",
            "response": '{"intentType": "产品推广", "confidence": 0.8, "extractedInfo": {"productInfo": "新产品"}}'
        },
        {
            "name": "带Markdown代码块",
            "response": '```json\n{"intentType": "客户分析", "confidence": 0.9, "extractedInfo": {"targetAudience": "年轻用户"}}\n```'
        },
        {
            "name": "带普通代码块",
            "response": '```\n{"intentType": "活动策划", "confidence": 0.7, "extractedInfo": {"marketingGoal": "提升品牌知名度"}}\n```'
        },
        {
            "name": "带额外文本",
            "response": '根据您的需求，我分析如下：\n```json\n{"intentType": "内容营销", "confidence": 0.85, "extractedInfo": {"content": "社交媒体内容"}}\n```\n希望这个分析对您有帮助。'
        },
        {
            "name": "复杂嵌套",
            "response": '分析结果：\n```json\n{"intentType": "综合营销", "confidence": 0.95, "extractedInfo": {"productInfo": "高端产品", "targetAudience": "高收入人群", "budget": "10万以上"}}\n```\n建议您考虑以下因素...'
        },
        {
            "name": "数组格式",
            "response": '```json\n[{"intentType": "推广", "confidence": 0.8}, {"intentType": "分析", "confidence": 0.6}]\n```'
        },
        {
            "name": "无代码块但有括号",
            "response": '根据分析，您的营销意图是：{"intentType": "品牌建设", "confidence": 0.75, "extractedInfo": {"marketingGoal": "建立品牌形象"}}，建议您...'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print(f"输入: {test_case['response'][:100]}...")
        
        try:
            result = agent._extract_json_from_response(test_case['response'])
            print(f"✓ 成功提取: {type(result).__name__}")
            print(f"  内容: {json.dumps(result, ensure_ascii=False, indent=2)[:200]}...")
        except Exception as e:
            print(f"✗ 提取失败: {e}")

def test_intent_analysis():
    """测试完整的意图分析功能"""
    print("\n=== 测试完整意图分析功能 ===")
    
    llm = getDefaultLLM()
    agent = IntentionAgent(llm)
    
    test_inputs = [
        "我想推广我们的新产品，目标用户是年轻白领，预算5万",
        "帮我分析一下我们的客户群体，看看如何提高转化率",
        "需要策划一个节日营销活动",
        "我想做内容营销，但不知道从哪里开始"
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n{i}. 用户输入: {user_input}")
        
        try:
            intent_info, missing_fields = agent.analyze_intent(user_input)
            
            print(f"✓ 意图类型: {intent_info.intentType}")
            print(f"  置信度: {intent_info.confidence}")
            print(f"  提取信息: {intent_info.extractedInfo}")
            print(f"  缺失核心字段: {missing_fields.missingCoreFields}")
            print(f"  缺失次要字段: {missing_fields.missingSecondaryFields}")
            print(f"  建议: {missing_fields.fieldSuggestions[:100]}...")
            print(f"  后续问题: {missing_fields.followupQuestions}")
            
        except Exception as e:
            print(f"✗ 分析失败: {e}")

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    llm = getDefaultLLM()
    agent = IntentionAgent(llm)
    
    error_cases = [
        {
            "name": "空响应",
            "response": ""
        },
        {
            "name": "无效JSON",
            "response": "这不是一个有效的JSON格式"
        },
        {
            "name": "不完整的JSON",
            "response": '{"intentType": "测试", "confidence": 0.8'
        },
        {
            "name": "无JSON内容",
            "response": "根据分析，您的需求很明确，建议您..."
        }
    ]
    
    for i, test_case in enumerate(error_cases, 1):
        print(f"\n{i}. 测试: {test_case['name']}")
        print(f"输入: {test_case['response']}")
        
        try:
            result = agent._extract_json_from_response(test_case['response'])
            print(f"✓ 意外成功: {result}")
        except Exception as e:
            print(f"✓ 正确抛出异常: {type(e).__name__}: {e}")

def test_performance():
    """测试性能"""
    print("\n=== 测试性能 ===")
    
    llm = getDefaultLLM()
    agent = IntentionAgent(llm)
    
    import time
    
    # 测试多次调用的性能
    test_input = "我想推广新产品，目标用户是年轻白领，预算5万"
    
    print(f"测试输入: {test_input}")
    print("执行5次分析，计算平均时间...")
    
    times = []
    for i in range(5):
        start_time = time.time()
        try:
            intent_info, missing_fields = agent.analyze_intent(test_input)
            end_time = time.time()
            duration = end_time - start_time
            times.append(duration)
            print(f"  第{i+1}次: {duration:.2f}秒")
        except Exception as e:
            print(f"  第{i+1}次: 失败 - {e}")
    
    if times:
        avg_time = sum(times) / len(times)
        print(f"平均执行时间: {avg_time:.2f}秒")

if __name__ == "__main__":
    print("开始测试IntentionAgent的智能JSON提取功能...")
    
    try:
        # 测试JSON提取
        test_json_extraction()
        
        # 测试完整分析
        test_intent_analysis()
        
        # 测试错误处理
        test_error_handling()
        
        # 测试性能
        test_performance()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
