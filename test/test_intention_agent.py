#!/usr/bin/env python3
"""
IntentionAgent测试脚本 - 测试新的意图识别Agent（全面引导话术版本）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.agents.IntentionAgent import IntentionAgent
from app.utils.models import MarketingState, IntentInfo, MissingFields

def test_intention_agent():
    """测试IntentionAgent的基本功能"""
    print("🚀 测试IntentionAgent")
    print("=" * 50)
    
    # 创建IntentionAgent实例
    agent = IntentionAgent(
        enableDetailedAnalysis=True,
        enableFieldSuggestions=True
    )
    print("✅ IntentionAgent创建成功")
    
    # 测试场景1: 简单输入
    print("\n📋 测试场景1: 简单输入")
    state1 = MarketingState(
        userInput="我想做一个拉新活动",
        currentStep="intention"
    )
    
    result1 = agent.execute(state1)
    print(f"执行结果: {result1.success}")
    print(f"意图信息: {state1.get('intentInfo')}")
    print(f"缺失字段: {state1.get('missingFields')}")
    
    # 测试全面引导话术
    guidance1 = state1.get("guidancePrompts", {})
    fieldSuggestions1 = state1.get("fieldSuggestions", {})
    followupQuestions1 = state1.get("followupQuestions", {})
    
    print("全面引导话术:")
    for key, value in guidance1.items():
        print(f"  {key}: {value}")
    
    print("字段建议:")
    for field, suggestions in fieldSuggestions1.items():
        print(f"  {field}: {suggestions}")
    
    print("追问问题:")
    for key, question in followupQuestions1.items():
        print(f"  {key}: {question}")
    
    # 测试场景2: 详细输入
    print("\n📋 测试场景2: 详细输入")
    state2 = MarketingState(
        userInput="我想做一个提升信用卡申请转化率的营销活动，目标客群是25-40岁的上班族，预算10万元",
        currentStep="intention"
    )
    
    result2 = agent.execute(state2)
    print(f"执行结果: {result2.success}")
    print(f"意图信息: {state2.get('intentInfo')}")
    print(f"缺失字段: {state2.get('missingFields')}")
    
    # 测试场景3: 有输入分析
    print("\n📋 测试场景3: 有输入分析")
    state3 = MarketingState(
        userInput="提升绑卡率活动",
        currentStep="intention",
        inputAnalysis={
            "complexity": "medium",
            "foundKeywords": ["提升", "绑卡率", "活动"],
            "intentHints": ["客户留存"]
        }
    )
    
    result3 = agent.execute(state3)
    print(f"执行结果: {result3.success}")
    print(f"意图信息: {state3.get('intentInfo')}")
    print(f"缺失字段: {state3.get('missingFields')}")
    
    print("\n✅ 测试完成")

def test_field_analysis():
    """测试字段分析功能"""
    print("\n🔍 测试字段分析功能")
    print("=" * 50)
    
    agent = IntentionAgent()
    
    # 测试完整意图信息
    intent_info = IntentInfo(
        bankCode="ICBC",
        bankName="工商银行",
        goal="提升绑卡率",
        activityType="营销活动",
        roi="10万元",
        audience="年轻用户",
        extra={
            "activityName": "支付宝绑卡立减活动",
            "period": "2024年Q3",
            "content": "绑卡送话费",
            "incentive": "话费",
            "channels": "短信+APP推送",
            "restriction": "每人限一次"
        }
    )
    
    missing_fields = agent._analyzeMissingFields(intent_info)
    print(f"完整意图的缺失字段: {missing_fields.missingCoreFields}")
    print(f"次要缺失字段: {missing_fields.missingSecondaryFields}")
    
    # 测试不完整意图信息
    incomplete_intent = IntentInfo(
        bankCode="ICBC",
        bankName="工商银行",
        goal="",  # 缺失
        activityType="营销活动",
        roi="",  # 缺失
        audience="",  # 缺失
        extra={}
    )
    
    missing_fields2 = agent._analyzeMissingFields(incomplete_intent)
    print(f"不完整意图的核心缺失字段: {missing_fields2.missingCoreFields}")
    print(f"次要缺失字段: {missing_fields2.missingSecondaryFields}")

def test_comprehensive_guidance():
    """测试全面引导话术生成功能"""
    print("\n💬 测试全面引导话术生成功能")
    print("=" * 50)
    
    agent = IntentionAgent(enableFieldSuggestions=True)
    
    # 测试场景1: 核心字段缺失
    print("\n📋 测试场景1: 核心字段缺失")
    state1 = MarketingState(
        userInput="我想做一个营销活动",
        currentStep="intention"
    )
    
    result1 = agent.execute(state1)
    guidance1 = state1.get("guidancePrompts", {})
    fieldSuggestions1 = state1.get("fieldSuggestions", {})
    followupQuestions1 = state1.get("followupQuestions", {})
    
    print("全面引导话术:")
    for key, value in guidance1.items():
        print(f"  {key}: {value}")
    
    print("字段建议:")
    for field, suggestions in fieldSuggestions1.items():
        print(f"  {field}: {suggestions}")
    
    print("追问问题:")
    for key, question in followupQuestions1.items():
        print(f"  {key}: {question}")
    
    # 测试场景2: 部分信息完整
    print("\n📋 测试场景2: 部分信息完整")
    state2 = MarketingState(
        userInput="我想做一个提升信用卡申请转化率的营销活动，目标客群是25-40岁的上班族",
        currentStep="intention"
    )
    
    result2 = agent.execute(state2)
    guidance2 = state2.get("guidancePrompts", {})
    fieldSuggestions2 = state2.get("fieldSuggestions", {})
    followupQuestions2 = state2.get("followupQuestions", {})
    
    print("全面引导话术:")
    for key, value in guidance2.items():
        print(f"  {key}: {value}")
    
    print("字段建议:")
    for field, suggestions in fieldSuggestions2.items():
        print(f"  {field}: {suggestions}")
    
    print("追问问题:")
    for key, question in followupQuestions2.items():
        print(f"  {key}: {question}")
    
    # 测试场景3: 次要字段缺失
    print("\n📋 测试场景3: 次要字段缺失")
    intent_info = IntentInfo(
        bankCode="ICBC",
        bankName="工商银行",
        goal="提升绑卡率",
        activityType="营销活动",
        roi="10万元",
        audience="年轻用户",
        extra={}
    )
    
    missing_fields = MissingFields(
        missingCoreFields=[],
        missingSecondaryFields=["activityName", "period", "incentive"],
        fieldDescriptions={},
        priority={}
    )
    
    guidance3 = agent._generateGuidancePrompts(intent_info, missing_fields, "提升绑卡率活动")
    print("生成的全面引导话术:")
    for key, value in guidance3.get("guidancePrompts", {}).items():
        print(f"  {key}: {value}")
    
    print("字段建议:")
    for field, suggestions in guidance3.get("fieldSuggestions", {}).items():
        print(f"  {field}: {suggestions}")
    
    print("追问问题:")
    for key, question in guidance3.get("followupQuestions", {}).items():
        print(f"  {key}: {question}")

def test_multi_turn_completion_and_flow():
    """测试多轮补全后系统能否顺利进入下一个业务节点及状态流转"""
    print("\n🔄 测试多轮补全与节点流转")
    print("=" * 50)
    agent = IntentionAgent(enableFieldSuggestions=True)
    
    # 第一步：用户输入缺失核心字段
    state = MarketingState(
        userInput="我想做一个提升信用卡申请转化率的营销活动",
        currentStep="intention"
    )
    result = agent.execute(state)
    print(f"首次执行，success: {result.success}, 缺失字段: {state.missingFields.missingCoreFields}")
    assert not result.success
    assert "budget" in state.missingFields.missingCoreFields

    # 第二步：用户补全budget
    state.userInput += "，预算10万元"
    result2 = agent.execute(state)
    print(f"第二次执行，success: {result2.success}, 缺失字段: {state.missingFields.missingCoreFields}")
    assert not result2.success
    assert "audience" in state.missingFields.missingCoreFields

    # 第三步：用户补全audience
    state.userInput += "，目标客群是新客户"
    result3 = agent.execute(state)
    print(f"第三次执行，success: {result3.success}, 缺失字段: {state.missingFields.missingCoreFields}")
    assert result3.success
    assert not state.missingFields.missingCoreFields

    # 检查状态流转
    print(f"当前步骤: {state.currentStep}")
    assert state.currentStep == "intentionCompleted"
    print("多轮补全与流转测试通过")

def main():
    """主测试函数"""
    print("🧪 IntentionAgent 全面测试")
    print("=" * 60)
    
    try:
        # 基本功能测试
        test_intention_agent()
        
        # 字段分析测试
        test_field_analysis()
        
        # 全面引导话术测试
        test_comprehensive_guidance()
        
        # 新增端到端多轮补全与流转测试
        test_multi_turn_completion_and_flow()
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
