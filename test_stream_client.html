<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销系统流式接口测试客户端 - 统一事件结构</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .input-section {
            margin-bottom: 20px;
        }
        .input-section input, .input-section textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .button-group {
            margin: 10px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .output-section {
            margin-top: 20px;
        }
        .event-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .event-item {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background: white;
        }
        .event-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .event-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status-connected {
            background: #d4edda;
            color: #155724;
        }
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .status-connecting {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 营销系统流式接口测试 - 统一事件结构</h1>

        <div class="info-section" style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff;">
            <h4>📋 新事件结构说明</h4>
            <p><strong>统一字段：</strong></p>
            <ul>
                <li><code>event</code>: 事件类型 (workflowStarted, nodeStarted, nodeFinished, message, businessInsight, userGuidance, workflowFinished, messageEnd, error)</li>
                <li><code>content</code>: 用户可读的文本内容</li>
                <li><code>data</code>: 结构化数据（程序处理用）</li>
                <li><code>messageId</code>: 消息ID</li>
                <li><code>conversationId</code>: 对话ID</li>
                <li><code>createdAt</code>: 创建时间戳</li>
            </ul>
        </div>

        <div class="input-section">
            <h3>请求参数</h3>
            <input type="text" id="userId" placeholder="用户ID" value="test_user_123">
            <input type="text" id="conversationId" placeholder="对话ID（可选）">
            <textarea id="userInput" placeholder="请输入营销需求..." rows="3">我想做一个暑期促销活动，目标是大学生群体，预算2万元</textarea>
        </div>
        
        <div class="button-group">
            <button class="btn btn-primary" onclick="startStreamChat()">开始流式对话</button>
            <button class="btn btn-secondary" onclick="startSyncChat()">同步对话（对比）</button>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status status-disconnected">未连接</div>
        
        <div class="output-section">
            <h3>实时事件日志</h3>
            <div id="eventLog" class="event-log"></div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let currentReader = null;
        let currentConversationId = null;

        // 服务器配置 - 统一管理服务器地址
        const SERVER_CONFIG = {
            // 根据当前环境自动选择服务器地址
            baseUrl: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                ? 'http://localhost:8000'  // 本地开发环境
                : 'http://h.1game.fun:28101',  // 生产环境

            // 备用服务器地址
            fallbackUrls: [
                'http://localhost:8000',
                'http://127.0.0.1:8000',
                'http://h.1game.fun:28101'
            ]
        };

        function updateStatus(message, type = 'disconnected') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status status-${type}`;
        }

        function addLogEntry(message, type = 'info') {
            const logEl = document.getElementById('eventLog');
            const entry = document.createElement('div');
            entry.className = `event-item ${type === 'error' ? 'event-error' : type === 'success' ? 'event-success' : ''}`;
            entry.innerHTML = `
                <strong>[${new Date().toLocaleTimeString()}]</strong> ${message}
            `;
            logEl.appendChild(entry);
            logEl.scrollTop = logEl.scrollHeight;
        }

        async function startStreamChat() {
            const userId = document.getElementById('userId').value;
            const userInput = document.getElementById('userInput').value;
            const conversationId = document.getElementById('conversationId').value || currentConversationId;

            if (!userId || !userInput) {
                alert('请填写用户ID和输入内容');
                return;
            }

            // 关闭之前的连接
            if (currentReader) {
                await currentReader.cancel();
                currentReader = null;
            }

            updateStatus('正在连接...', 'connecting');
            addLogEntry('开始建立流式连接...', 'info');

            try {
                // 构建POST请求
                const requestBody = {
                    userId: userId,
                    userInput: userInput,
                    ...(conversationId && { conversationId: conversationId })
                };

                addLogEntry(`请求数据: ${JSON.stringify(requestBody, null, 2)}`, 'info');

                // 发起POST请求 - 使用统一的服务器配置
                const streamUrl = `${SERVER_CONFIG.baseUrl}/api/marketing/chat/stream`;
                addLogEntry(`连接到: ${streamUrl}`, 'info');

                const response = await fetch(streamUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                        'Origin': window.location.origin  // 明确设置Origin头
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                updateStatus('已连接，等待响应...', 'connected');
                addLogEntry('流式连接已建立', 'success');

                // 处理流式响应
                currentReader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await currentReader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留最后一个可能不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                handleStreamEvent(data);
                            } catch (e) {
                                addLogEntry(`解析事件数据失败: ${e.message}`, 'error');
                            }
                        }
                    }
                }

            } catch (error) {
                updateStatus('连接错误', 'disconnected');
                addLogEntry(`连接失败: ${error.message}`, 'error');
                console.error('Stream Error:', error);
            }
        }

        function handleStreamEvent(data) {
            // 新的统一事件结构：event, content, data, messageId, conversationId, createdAt
            const eventType = data.event;
            const content = data.content || '';
            const eventData = data.data || {};
            const messageId = data.messageId || '';
            const conversationId = data.conversationId || '';
            const createdAt = data.createdAt || '';

            // 保存会话ID
            if (conversationId) {
                currentConversationId = conversationId;
                document.getElementById('conversationId').value = conversationId;
            }

            // 显示基础事件信息
            addLogEntry(`📨 [${eventType}] ${content}`, 'info');

            switch (eventType) {
                case 'workflowStarted':
                    updateStatus('工作流开始执行...', 'connecting');
                    addLogEntry(`🚀 ${content}`, 'success');
                    if (eventData.totalSteps) {
                        addLogEntry(`📊 总步数: ${eventData.totalSteps}`, 'info');
                    }
                    break;

                case 'nodeStarted':
                    handleNodeStarted(data);
                    break;

                case 'nodeFinished':
                    handleNodeFinished(data);
                    break;

                case 'message':
                    handleMessage(data);
                    break;

                case 'businessInsight':
                    handleBusinessInsight(data);
                    break;

                case 'userGuidance':
                    handleUserGuidance(data);
                    break;

                case 'workflowFinished':
                    handleWorkflowFinished(data);
                    break;

                case 'messageEnd':
                    handleMessageEnd(data);
                    break;

                case 'error':
                    handleError(data);
                    break;

                case 'ping':
                    // 静默处理保活事件
                    break;

                default:
                    addLogEntry(`📝 未知事件类型: ${eventType} - ${content}`, 'info');
            }
        }

        function handleNodeStarted(data) {
            const eventData = data.data || {};
            const nodeId = eventData.nodeId || '';
            const displayName = eventData.displayName || getNodeDisplayName(nodeId);
            const stepNumber = eventData.stepNumber || 0;
            const progressPercent = eventData.progressPercent || 0;

            addLogEntry(`🔄 ${displayName} 开始执行 (步骤 ${stepNumber})`, 'info');

            // 更新进度条
            if (progressPercent > 0) {
                updateProgress(progressPercent);
            }
        }

        function handleNodeFinished(data) {
            const eventData = data.data || {};
            const nodeId = eventData.nodeId || '';
            const displayName = eventData.displayName || getNodeDisplayName(nodeId);
            const status = eventData.status || '';
            const elapsedTime = eventData.elapsedTime || 0;
            const outputs = eventData.outputs || {};

            const statusIcon = status === 'succeeded' ? '✅' : status === 'failed' ? '❌' : '⏸️';
            addLogEntry(`${statusIcon} ${displayName} 执行${status} (耗时: ${elapsedTime.toFixed(2)}s)`,
                       status === 'succeeded' ? 'success' : 'error');

            // 显示节点输出
            if (Object.keys(outputs).length > 0) {
                const resultSummary = JSON.stringify(outputs, null, 2);
                if (resultSummary.length > 200) {
                    addLogEntry(`📊 节点输出 (点击展开): ${resultSummary.substring(0, 200)}...`, 'info');
                } else {
                    addLogEntry(`📊 节点输出: ${resultSummary}`, 'info');
                }
            }
        }

        function handleMessage(data) {
            const content = data.content || '';

            if (content.trim()) {
                // 累积LLM输出内容
                if (!window.llmOutput) {
                    window.llmOutput = '';
                    addLogEntry('🤖 LLM开始生成内容...', 'info');
                }
                window.llmOutput += content;

                // 显示累积的内容（限制长度）
                const displayContent = window.llmOutput.length > 200
                    ? window.llmOutput.substring(0, 200) + '...'
                    : window.llmOutput;

                // 更新最后一个LLM日志条目
                updateLastLLMEntry(`💬 LLM输出: ${displayContent}`);
            }
        }

        function handleBusinessInsight(data) {
            const eventData = data.data || {};
            const nodeId = eventData.nodeId || '';
            const insightType = eventData.insightType || '';
            const displayName = eventData.displayName || getNodeDisplayName(nodeId);

            addLogEntry(`💡 ${displayName} 业务洞察 (${insightType})`, 'insight');

            // 显示洞察数据
            const insightKeys = Object.keys(eventData).filter(key =>
                !['nodeId', 'insightType', 'displayName'].includes(key)
            );

            if (insightKeys.length > 0) {
                // 根据不同的洞察类型显示不同的信息
                if (insightType === 'intentionAnalysis') {
                    if (eventData.intentType) {
                        addLogEntry(`🎯 意图类型: ${eventData.intentType}`, 'info');
                    }
                    if (eventData.confidence) {
                        addLogEntry(`📊 置信度: ${eventData.confidence}`, 'info');
                    }
                    if (eventData.targetAudience) {
                        addLogEntry(`👥 目标客群: ${eventData.targetAudience}`, 'info');
                    }
                } else if (insightType === 'customerAnalysis') {
                    if (eventData.audience) {
                        addLogEntry(`👥 客群分析: ${eventData.audience}`, 'info');
                    }
                    if (eventData.channels && eventData.channels.length > 0) {
                        addLogEntry(`📢 推荐渠道: ${eventData.channels.join(', ')}`, 'info');
                    }
                } else if (insightType === 'planRecommendation') {
                    if (eventData.planTitle) {
                        addLogEntry(`📋 方案标题: ${eventData.planTitle}`, 'info');
                    }
                    if (eventData.expectedROI) {
                        addLogEntry(`📈 预期ROI: ${eventData.expectedROI}`, 'info');
                    }
                } else {
                    // 通用显示
                    const insightData = {};
                    insightKeys.forEach(key => {
                        insightData[key] = eventData[key];
                    });
                    addLogEntry(`📈 洞察数据:\n${JSON.stringify(insightData, null, 2)}`, 'insight');
                }
            }
        }

        function handleUserGuidance(data) {
            const eventData = data.data || {};
            const guidanceType = eventData.guidanceType || '';
            const missingFields = eventData.missingFields || [];
            const interactionData = eventData.interactionData || {};

            addLogEntry(`🧭 用户引导 (${guidanceType}): ${data.content}`, 'guidance');

            if (missingFields.length > 0) {
                addLogEntry(`📝 缺失字段: ${missingFields.join(', ')}`, 'guidance');
            }

            // 显示建议选项
            if (interactionData.suggestedResponses && interactionData.suggestedResponses.length > 0) {
                addLogEntry(`💡 建议选项: ${interactionData.suggestedResponses.join(' | ')}`, 'guidance');
            }

            // 显示表单字段
            if (interactionData.formFields && interactionData.formFields.length > 0) {
                addLogEntry(`📋 表单字段: ${interactionData.formFields.length}个`, 'guidance');
            }
        }

        function handleWorkflowFinished(data) {
            const eventData = data.data || {};
            const status = eventData.status || '';
            const elapsedTime = eventData.elapsedTime || 0;
            const totalSteps = eventData.totalSteps || 0;
            const completedSteps = eventData.completedSteps || 0;

            if (status === 'succeeded') {
                updateStatus('工作流执行完成', 'connected');
                addLogEntry(`🎉 ${data.content} (耗时: ${elapsedTime.toFixed(2)}s)`, 'success');
                addLogEntry(`📊 执行统计: ${completedSteps}/${totalSteps} 步骤完成`, 'info');
            } else if (status === 'failed') {
                updateStatus('工作流执行失败', 'disconnected');
                addLogEntry(`❌ ${data.content}`, 'error');
                if (eventData.error) {
                    addLogEntry(`🔍 错误详情: ${eventData.error}`, 'error');
                }
            } else if (status === 'interrupted') {
                updateStatus('工作流已中断，等待用户输入', 'connecting');
                addLogEntry(`⏸️ ${data.content}`, 'info');
                addLogEntry(`📊 进度: ${completedSteps}/${totalSteps} 步骤已完成`, 'info');
            }

            // 停止流式连接
            if (currentReader) {
                currentReader.cancel();
                currentReader = null;
            }
        }

        function handleMessageEnd(data) {
            const eventData = data.data || {};
            const totalEvents = eventData.totalEvents || 0;
            const executionTime = eventData.executionTime || 0;

            addLogEntry(`✨ ${data.content}`, 'success');
            addLogEntry(`📊 统计: 共处理 ${totalEvents} 个事件，耗时 ${executionTime.toFixed(2)}s`, 'info');

            // 重置LLM输出累积
            window.llmOutput = '';
        }

        function handleError(data) {
            const eventData = data.data || {};
            const errorCode = eventData.errorCode || '';
            const canRetry = eventData.canRetry || false;

            updateStatus('发生错误', 'disconnected');
            addLogEntry(`❌ ${data.content}`, 'error');

            if (errorCode) {
                addLogEntry(`🔍 错误码: ${errorCode}`, 'error');
            }

            if (canRetry) {
                addLogEntry(`🔄 可以重试`, 'info');
            }

            // 停止流式连接
            if (currentReader) {
                currentReader.cancel();
                currentReader = null;
            }
        }

        // === 辅助函数 ===

        function updateProgress(percent) {
            // 更新进度条（如果有的话）
            const progressBar = document.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = `${percent}%`;
                progressBar.textContent = `${percent}%`;
            }
        }

        function updateLastLLMEntry(content) {
            // 更新最后一个LLM日志条目，实现打字机效果
            const logContainer = document.getElementById('eventLog');

            // 检查容器是否存在
            if (!logContainer) {
                console.error('eventLog container not found');
                return;
            }

            const lastEntry = logContainer.lastElementChild;

            // 检查是否有最后一个条目，并且是LLM输出
            if (lastEntry && lastEntry.textContent && lastEntry.textContent.includes('💬 LLM输出:')) {
                lastEntry.innerHTML = `<span class="timestamp">[${new Date().toLocaleTimeString()}]</span> ${content}`;
            } else {
                // 如果没有找到LLM输出条目，创建新的
                addLogEntry(content, 'info');
            }
        }

        function getNodeDisplayName(nodeName) {
            const displayNames = {
                'ROUTER_AGENT': '智能路由',
                'INTENTION_AGENT': '意图识别',
                'TAG_CUSTOMER_AGENT': '客群分析',
                'PLAN_CREATOR_AGENT': '方案生成',
                'HISTORY_RAG_AGENT': '历史数据检索'
            };
            return displayNames[nodeName] || nodeName;
        }

        async function startSyncChat() {
            const userId = document.getElementById('userId').value;
            const userInput = document.getElementById('userInput').value;
            const conversationId = document.getElementById('conversationId').value || currentConversationId;

            if (!userId || !userInput) {
                alert('请填写用户ID和输入内容');
                return;
            }

            updateStatus('同步请求处理中...', 'connecting');
            addLogEntry('发送同步请求...', 'info');

            try {
                // 使用统一的服务器配置
                const syncUrl = `${SERVER_CONFIG.baseUrl}/api/marketing/chat`;
                addLogEntry(`同步请求到: ${syncUrl}`, 'info');

                const response = await fetch(syncUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin  // 明确设置Origin头
                    },
                    body: JSON.stringify({
                        userId: userId,
                        userInput: userInput,
                        ...(conversationId && { conversationId: conversationId })
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    updateStatus('同步请求完成', 'connected');
                    addLogEntry('🎉 同步请求成功完成', 'success');
                    addLogEntry(`📊 响应结果: ${JSON.stringify(result, null, 2)}`, 'info');
                    
                    // 保存会话ID
                    if (result.conversationId) {
                        currentConversationId = result.conversationId;
                        document.getElementById('conversationId').value = result.conversationId;
                    }
                } else {
                    updateStatus('同步请求失败', 'disconnected');
                    addLogEntry(`❌ 同步请求失败: ${result.message}`, 'error');
                }
            } catch (error) {
                updateStatus('同步请求错误', 'disconnected');
                addLogEntry(`❌ 同步请求错误: ${error.message}`, 'error');
            }
        }



        function clearLog() {
            document.getElementById('eventLog').innerHTML = '';
            updateStatus('未连接', 'disconnected');
        }

        // 页面加载时的初始化
        window.onload = function() {
            addLogEntry('页面加载完成，可以开始测试', 'info');
        };

        // 页面关闭时清理连接
        window.onbeforeunload = function() {
            if (currentReader) {
                currentReader.cancel();
            }
        };
    </script>
</body>
</html>
