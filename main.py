"""
智能营销系统主入口，只负责启动API服务器
"""

import sys
from pathlib import Path
import logging
import uvicorn

from app.core import config

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

if __name__ == "__main__":
    print(f"""
🚀 智能营销方案生成系统 API 服务器启动中...

📋 服务信息:
   - 主机: {config.HOST}
   - 端口: {config.PORT}

📖 API文档:
   - Swagger UI: http://{config.HOST}:{config.PORT}/docs
   - ReDoc: http://{config.HOST}:{config.PORT}/redoc
   - OpenAPI: http://{config.HOST}:{config.PORT}/openapi.json

🔧 健康检查:
   - 健康状态: http://{config.HOST}:{config.PORT}/api/v1/health

""")
    try:
        uvicorn.run(
            "app.api.app:app",  # 以字符串方式传递app，支持reload
            host=config.HOST,
            port=config.PORT,
            reload=True ,
            log_level=getattr(config, 'LOG_LEVEL', 'info'),
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 API服务器已停止")
    except Exception as e:
        print(f"❌ API服务器启动失败: {str(e)}")
        sys.exit(1)
